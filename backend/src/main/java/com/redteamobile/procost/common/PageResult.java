package com.redteamobile.procost.common;

import java.util.List;

/**
 * 分页结果类
 */
public class PageResult<T> {
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Integer current;
    
    /**
     * 每页大小
     */
    private Integer size;
    
    /**
     * 总页数（自动计算）
     */
    private Integer pages;
    
    /**
     * 无参构造函数
     */
    public PageResult() {
    }
    
    /**
     * 构造函数
     */
    public PageResult(List<T> records, Long total, Integer current, Integer size) {
        this.records = records;
        this.total = total;
        this.current = current;
        this.size = size;
        this.pages = calculatePages(total, size);
    }
    
    /**
     * 计算总页数
     */
    private static Integer calculatePages(Long total, Integer size) {
        if (size == null || size <= 0 || total == null || total <= 0) {
            return 0;
        }
        return (int) Math.ceil((double) total / size);
    }
    
    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> records, Long total, Integer current, Integer size) {
        return new PageResult<T>(records, total, current, size);
    }
    
    /**
     * 创建空分页结果
     */
    public static <T> PageResult<T> empty(Integer current, Integer size) {
        return new PageResult<T>(java.util.Collections.emptyList(), 0L, current, size);
    }
    

    
    /**
     * 是否有下一页
     */
    public boolean hasNext() {
        return current != null && pages != null && current < pages;
    }
    
    /**
     * 是否有上一页
     */
    public boolean hasPrevious() {
        return current != null && current > 1;
    }
    
    /**
     * 是否为空
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }
    
    // Getter 和 Setter 方法
    public List<T> getRecords() {
        return records;
    }
    
    public void setRecords(List<T> records) {
        this.records = records;
    }
    
    public Long getTotal() {
        return total;
    }
    
    public void setTotal(Long total) {
        this.total = total;
        this.pages = calculatePages(this.total, this.size);
    }
    
    public Integer getCurrent() {
        return current;
    }
    
    public void setCurrent(Integer current) {
        this.current = current;
    }
    
    public Integer getSize() {
        return size;
    }
    
    public void setSize(Integer size) {
        this.size = size;
        this.pages = calculatePages(this.total, this.size);
    }
    
    public Integer getPages() {
        return pages;
    }
    
    public void setPages(Integer pages) {
        this.pages = pages;
    }
}
