package com.redteamobile.procost.service;

import com.redteamobile.procost.entity.Inventory;
import com.redteamobile.procost.common.PageResult;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存服务接口
 */
public interface InventoryService {
    
    /**
     * 根据ID查询库存
     */
    Inventory findById(Long id);
    
    /**
     * 根据物料规格ID查询库存
     */
    Inventory findByMaterialSpecId(Long materialSpecId);
    
    /**
     * 查询所有库存
     */
    List<Inventory> findAll();
    
    /**
     * 分页查询库存
     */
    PageResult<Inventory> findByPage(Long materialSpecId,
                                    String category,
                                    String cardType,
                                    String environment,
                                    String manufacturer,
                                    Integer minStock,
                                    Integer maxStock,
                                    Integer page,
                                    Integer size);
    
    /**
     * 查询库存预警列表（当前库存 <= 最小库存预警）
     */
    List<Inventory> findLowStockInventories();
    
    /**
     * 创建库存记录
     */
    Inventory create(Inventory inventory);
    
    /**
     * 更新库存信息
     */
    Inventory update(Inventory inventory);
    
    /**
     * 更新库存数量
     */
    void updateStock(Long materialSpecId, Integer currentStock, Integer availableStock, Integer reservedStock);
    
    /**
     * 增加库存（入库）
     */
    void increaseStock(Long materialSpecId, Integer quantity);
    
    /**
     * 减少库存（出库）
     */
    void decreaseStock(Long materialSpecId, Integer quantity);
    
    /**
     * 更新最后入库时间
     */
    void updateLastInTime(Long materialSpecId, LocalDateTime lastInTime);
    
    /**
     * 更新最后出库时间
     */
    void updateLastOutTime(Long materialSpecId, LocalDateTime lastOutTime);
    
    /**
     * 根据ID删除库存
     */
    void deleteById(Long id);
    
    /**
     * 根据物料规格ID删除库存
     */
    void deleteByMaterialSpecId(Long materialSpecId);
    
    /**
     * 统计库存总数
     */
    Long countAll();
    
    /**
     * 统计预警库存数量
     */
    Long countLowStock();
    
    /**
     * 导出库存数据
     */
    List<Inventory> exportData(Long materialSpecId,
                              String category,
                              String cardType,
                              String environment,
                              String manufacturer,
                              Integer minStock,
                              Integer maxStock);
}
