package com.redteamobile.procost.service;

import com.redteamobile.procost.entity.Supplier;
import com.redteamobile.procost.common.PageResult;
import java.util.List;

/**
 * 供应商服务接口
 */
public interface SupplierService {
    
    /**
     * 根据ID查询供应商
     */
    Supplier findById(Long id);
    
    /**
     * 根据供应商编码查询供应商
     */
    Supplier findBySupplierCode(String supplierCode);
    
    /**
     * 查询所有供应商
     */
    List<Supplier> findAll();
    
    /**
     * 根据状态查询供应商
     */
    List<Supplier> findByStatus(Integer status);
    
    /**
     * 分页查询供应商
     */
    PageResult<Supplier> findByPage(String supplierCode,
                                   String supplierName,
                                   String address,
                                   Integer status,
                                   Integer page,
                                   Integer size);
    
    /**
     * 创建供应商
     */
    Supplier create(Supplier supplier);
    
    /**
     * 更新供应商
     */
    Supplier update(Supplier supplier);
    
    /**
     * 根据ID删除供应商
     */
    void deleteById(Long id);
    
    /**
     * 检查供应商编码是否存在
     */
    boolean existsBySupplierCode(String supplierCode);
    
    /**
     * 检查供应商编码是否存在（排除指定ID）
     */
    boolean existsBySupplierCodeExcludeId(String supplierCode, Long excludeId);
    
    /**
     * 根据状态统计供应商数量
     */
    int countByStatus(Integer status);
    
    /**
     * 批量导入供应商
     */
    void batchImport(List<Supplier> suppliers);
    
    /**
     * 导出供应商数据
     */
    List<Supplier> exportData(String supplierCode,
                             String supplierName,
                             String address,
                             Integer status);
}
