package com.redteamobile.procost.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redteamobile.procost.entity.Inventory;
import com.redteamobile.procost.mapper.InventoryMapper;
import com.redteamobile.procost.service.InventoryService;
import com.redteamobile.procost.common.PageResult;
import com.redteamobile.procost.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryServiceImpl implements InventoryService {
    
    private final InventoryMapper inventoryMapper;
    
    @Override
    public Inventory findById(Long id) {
        if (id == null) {
            throw new BusinessException("库存ID不能为空");
        }
        Inventory inventory = inventoryMapper.selectById(id);
        if (inventory == null) {
            throw new BusinessException("库存记录不存在");
        }
        return inventory;
    }
    
    @Override
    public Inventory findByMaterialSpecId(Long materialSpecId) {
        if (materialSpecId == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        return inventoryMapper.findByMaterialSpecId(materialSpecId);
    }
    
    @Override
    public List<Inventory> findAll() {
        return inventoryMapper.findAllWithSpec();
    }
    
    @Override
    public PageResult<Inventory> findByPage(Long materialSpecId, String category, String cardType,
                                           String environment, String manufacturer, Integer minStock,
                                           Integer maxStock, Integer page, Integer size) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        // 使用 MyBatis-Plus 分页
        Page<Inventory> pageParam = new Page<>(page, size);
        IPage<Inventory> pageResult = inventoryMapper.findByPage(pageParam, materialSpecId, category, cardType,
                                                               environment, manufacturer, minStock, maxStock);
        
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal(), page, size);
    }
    
    @Override
    public List<Inventory> findLowStockInventories() {
        return inventoryMapper.findLowStockInventories();
    }
    
    @Override
    @Transactional
    public Inventory create(Inventory inventory) {
        validateInventory(inventory);
        
        // 检查是否已存在该物料规格的库存记录
        Inventory existingInventory = findByMaterialSpecId(inventory.getMaterialSpecId());
        if (existingInventory != null) {
            throw new BusinessException("该物料规格的库存记录已存在");
        }
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        inventory.setCreateTime(now);
        inventory.setUpdateTime(now);
        
        // 设置默认值
        if (inventory.getCurrentStock() == null) {
            inventory.setCurrentStock(0);
        }
        if (inventory.getAvailableStock() == null) {
            inventory.setAvailableStock(inventory.getCurrentStock());
        }
        if (inventory.getReservedStock() == null) {
            inventory.setReservedStock(0);
        }
        if (inventory.getMinStock() == null) {
            inventory.setMinStock(0);
        }
        if (inventory.getMaxStock() == null) {
            inventory.setMaxStock(0);
        }
        
        int result = inventoryMapper.insert(inventory);
        if (result <= 0) {
            throw new BusinessException("创建库存记录失败");
        }
        
        log.info("创建库存记录成功，ID: {}, 物料规格ID: {}", inventory.getId(), inventory.getMaterialSpecId());
        return inventory;
    }
    
    @Override
    @Transactional
    public Inventory update(Inventory inventory) {
        if (inventory.getId() == null) {
            throw new BusinessException("库存ID不能为空");
        }
        
        // 检查库存记录是否存在
        Inventory existingInventory = findById(inventory.getId());
        
        validateInventory(inventory);
        
        // 设置更新时间
        inventory.setUpdateTime(LocalDateTime.now());
        
        int result = inventoryMapper.update(new UpdateWrapper<>(inventory));
        if (result <= 0) {
            throw new BusinessException("更新库存记录失败");
        }
        
        log.info("更新库存记录成功，ID: {}, 物料规格ID: {}", inventory.getId(), inventory.getMaterialSpecId());
        return findById(inventory.getId());
    }
    
    @Override
    @Transactional
    public void updateStock(Long materialSpecId, Integer currentStock, Integer availableStock, Integer reservedStock) {
        if (materialSpecId == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        
        int result = inventoryMapper.updateStock(materialSpecId, currentStock, availableStock, reservedStock);
        if (result <= 0) {
            throw new BusinessException("更新库存数量失败");
        }
        
        log.info("更新库存数量成功，物料规格ID: {}, 当前库存: {}", materialSpecId, currentStock);
    }
    
    @Override
    @Transactional
    public void increaseStock(Long materialSpecId, Integer quantity) {
        if (materialSpecId == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        if (quantity == null || quantity <= 0) {
            throw new BusinessException("入库数量必须大于0");
        }
        
        Inventory inventory = findByMaterialSpecId(materialSpecId);
        if (inventory == null) {
            throw new BusinessException("库存记录不存在");
        }
        
        int newCurrentStock = inventory.getCurrentStock() + quantity;
        int newAvailableStock = inventory.getAvailableStock() + quantity;
        
        updateStock(materialSpecId, newCurrentStock, newAvailableStock, inventory.getReservedStock());
        updateLastInTime(materialSpecId, LocalDateTime.now());
        
        log.info("入库成功，物料规格ID: {}, 入库数量: {}, 当前库存: {}", materialSpecId, quantity, newCurrentStock);
    }
    
    @Override
    @Transactional
    public void decreaseStock(Long materialSpecId, Integer quantity) {
        if (materialSpecId == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        if (quantity == null || quantity <= 0) {
            throw new BusinessException("出库数量必须大于0");
        }
        
        Inventory inventory = findByMaterialSpecId(materialSpecId);
        if (inventory == null) {
            throw new BusinessException("库存记录不存在");
        }
        
        if (inventory.getAvailableStock() < quantity) {
            throw new BusinessException("可用库存不足，当前可用库存: " + inventory.getAvailableStock());
        }
        
        int newCurrentStock = inventory.getCurrentStock() - quantity;
        int newAvailableStock = inventory.getAvailableStock() - quantity;
        
        if (newCurrentStock < 0) {
            throw new BusinessException("库存不足");
        }
        
        updateStock(materialSpecId, newCurrentStock, newAvailableStock, inventory.getReservedStock());
        updateLastOutTime(materialSpecId, LocalDateTime.now());
        
        log.info("出库成功，物料规格ID: {}, 出库数量: {}, 当前库存: {}", materialSpecId, quantity, newCurrentStock);
    }
    
    @Override
    @Transactional
    public void updateLastInTime(Long materialSpecId, LocalDateTime lastInTime) {
        if (materialSpecId == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        
        inventoryMapper.updateLastInTime(materialSpecId, lastInTime);
    }
    
    @Override
    @Transactional
    public void updateLastOutTime(Long materialSpecId, LocalDateTime lastOutTime) {
        if (materialSpecId == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        
        inventoryMapper.updateLastOutTime(materialSpecId, lastOutTime);
    }
    
    @Override
    @Transactional
    public void deleteById(Long id) {
        if (id == null) {
            throw new BusinessException("库存ID不能为空");
        }
        
        // 检查库存记录是否存在
        Inventory inventory = findById(id);
        
        int result = inventoryMapper.deleteById(id);
        if (result <= 0) {
            throw new BusinessException("删除库存记录失败");
        }
        
        log.info("删除库存记录成功，ID: {}, 物料规格ID: {}", id, inventory.getMaterialSpecId());
    }
    
    @Override
    @Transactional
    public void deleteByMaterialSpecId(Long materialSpecId) {
        if (materialSpecId == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        
        inventoryMapper.deleteByMaterialSpecId(materialSpecId);
        log.info("删除物料规格库存记录成功，物料规格ID: {}", materialSpecId);
    }
    
    @Override
    public Long countAll() {
        return inventoryMapper.countAll();
    }
    
    @Override
    public Long countLowStock() {
        return inventoryMapper.countLowStock();
    }
    
    @Override
    public List<Inventory> exportData(Long materialSpecId, String category, String cardType,
                                     String environment, String manufacturer, Integer minStock,
                                     Integer maxStock) {
        // 导出时不分页，获取所有数据
        Page<Inventory> pageParam = new Page<>(1, Integer.MAX_VALUE);
        IPage<Inventory> pageResult = inventoryMapper.findByPage(pageParam, materialSpecId, category, cardType,
                                                               environment, manufacturer, minStock, maxStock);
        return pageResult.getRecords();
    }
    
    /**
     * 验证库存数据
     */
    private void validateInventory(Inventory inventory) {
        if (inventory == null) {
            throw new BusinessException("库存信息不能为空");
        }
        
        if (inventory.getMaterialSpecId() == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        
        if (inventory.getCurrentStock() != null && inventory.getCurrentStock() < 0) {
            throw new BusinessException("当前库存数量不能为负数");
        }
        
        if (inventory.getAvailableStock() != null && inventory.getAvailableStock() < 0) {
            throw new BusinessException("可用库存数量不能为负数");
        }
        
        if (inventory.getReservedStock() != null && inventory.getReservedStock() < 0) {
            throw new BusinessException("预留库存数量不能为负数");
        }
        
        if (inventory.getMinStock() != null && inventory.getMinStock() < 0) {
            throw new BusinessException("最小库存预警不能为负数");
        }
        
        if (inventory.getMaxStock() != null && inventory.getMaxStock() < 0) {
            throw new BusinessException("最大库存上限不能为负数");
        }
    }
}
