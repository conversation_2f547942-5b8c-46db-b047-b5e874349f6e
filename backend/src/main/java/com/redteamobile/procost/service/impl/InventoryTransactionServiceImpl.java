package com.redteamobile.procost.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redteamobile.procost.entity.InventoryTransaction;
import com.redteamobile.procost.mapper.InventoryTransactionMapper;
import com.redteamobile.procost.service.InventoryTransactionService;
import com.redteamobile.procost.service.InventoryService;
import com.redteamobile.procost.common.PageResult;
import com.redteamobile.procost.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 出入库记录服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryTransactionServiceImpl implements InventoryTransactionService {
    
    private final InventoryTransactionMapper inventoryTransactionMapper;
    private final InventoryService inventoryService;
    
    @Override
    public InventoryTransaction findById(Long id) {
        if (id == null) {
            throw new BusinessException("出入库记录ID不能为空");
        }
        InventoryTransaction transaction = inventoryTransactionMapper.findById(id);
        if (transaction == null) {
            throw new BusinessException("出入库记录不存在");
        }
        return transaction;
    }
    
    @Override
    public InventoryTransaction findByTransactionNum(String transactionNum) {
        if (!StringUtils.hasText(transactionNum)) {
            throw new BusinessException("事务单号不能为空");
        }
        return inventoryTransactionMapper.findByTransactionNum(transactionNum);
    }
    
    @Override
    public List<InventoryTransaction> findByMaterialId(Long materialId) {
        if (materialId == null) {
            throw new BusinessException("物料ID不能为空");
        }
        return inventoryTransactionMapper.findByMaterialId(materialId);
    }
    
    @Override
    public List<InventoryTransaction> findByMaterialSpecId(Long materialSpecId) {
        if (materialSpecId == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        return inventoryTransactionMapper.findByMaterialSpecId(materialSpecId);
    }
    
    @Override
    public PageResult<InventoryTransaction> findByPage(String transactionNum, String eid, String specName,
                                                      Integer transactionType, String orderNum, Long supplierId,
                                                      String operatorId, LocalDateTime startDate, LocalDateTime endDate,
                                                      Integer status, Integer page, Integer size) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        // 使用 MyBatis-Plus 分页
        Page<InventoryTransaction> pageParam = new Page<>(page, size);
        IPage<InventoryTransaction> pageResult = inventoryTransactionMapper.findByPage(pageParam, transactionNum, eid, specName,
                                                                                      transactionType, orderNum, supplierId,
                                                                                      operatorId, startDate, endDate, status);
        
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal(), page, size);
    }
    
    @Override
    @Transactional
    public InventoryTransaction create(InventoryTransaction transaction) {
        validateTransaction(transaction);
        
        // 检查事务单号是否已存在
        if (existsByTransactionNum(transaction.getTransactionNum())) {
            throw new BusinessException("事务单号已存在: " + transaction.getTransactionNum());
        }
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        transaction.setCreateTime(now);
        transaction.setUpdateTime(now);
        
        // 设置默认状态
        if (transaction.getStatus() == null) {
            transaction.setStatus(1);
        }
        
        // 设置事务日期
        if (transaction.getTransactionDate() == null) {
            transaction.setTransactionDate(now);
        }
        
        int result = inventoryTransactionMapper.insert(transaction);
        if (result <= 0) {
            throw new BusinessException("创建出入库记录失败");
        }
        
        log.info("创建出入库记录成功，ID: {}, 事务单号: {}", transaction.getId(), transaction.getTransactionNum());
        return transaction;
    }
    
    @Override
    @Transactional
    public InventoryTransaction update(InventoryTransaction transaction) {
        if (transaction.getId() == null) {
            throw new BusinessException("出入库记录ID不能为空");
        }
        
        // 检查记录是否存在
        InventoryTransaction existingTransaction = findById(transaction.getId());
        
        validateTransaction(transaction);
        
        // 检查事务单号是否已被其他记录使用
        if (existsByTransactionNumExcludeId(transaction.getTransactionNum(), transaction.getId())) {
            throw new BusinessException("事务单号已被其他记录使用: " + transaction.getTransactionNum());
        }
        
        // 设置更新时间
        transaction.setUpdateTime(LocalDateTime.now());
        
        int result = inventoryTransactionMapper.update(transaction);
        if (result <= 0) {
            throw new BusinessException("更新出入库记录失败");
        }
        
        log.info("更新出入库记录成功，ID: {}, 事务单号: {}", transaction.getId(), transaction.getTransactionNum());
        return findById(transaction.getId());
    }
    
    @Override
    @Transactional
    public void updateStatus(Long id, Integer status) {
        if (id == null) {
            throw new BusinessException("出入库记录ID不能为空");
        }
        if (status == null) {
            throw new BusinessException("状态不能为空");
        }
        
        int result = inventoryTransactionMapper.updateStatus(id, status);
        if (result <= 0) {
            throw new BusinessException("更新记录状态失败");
        }
        
        log.info("更新出入库记录状态成功，ID: {}, 状态: {}", id, status);
    }
    
    @Override
    @Transactional
    public void deleteById(Long id) {
        if (id == null) {
            throw new BusinessException("出入库记录ID不能为空");
        }
        
        // 检查记录是否存在
        InventoryTransaction transaction = findById(id);
        
        int result = inventoryTransactionMapper.deleteById(id);
        if (result <= 0) {
            throw new BusinessException("删除出入库记录失败");
        }
        
        log.info("删除出入库记录成功，ID: {}, 事务单号: {}", id, transaction.getTransactionNum());
    }
    
    @Override
    public boolean existsByTransactionNum(String transactionNum) {
        if (!StringUtils.hasText(transactionNum)) {
            return false;
        }
        return inventoryTransactionMapper.existsByTransactionNum(transactionNum) > 0;
    }
    
    @Override
    public boolean existsByTransactionNumExcludeId(String transactionNum, Long excludeId) {
        if (!StringUtils.hasText(transactionNum) || excludeId == null) {
            return false;
        }
        return inventoryTransactionMapper.existsByTransactionNumExcludeId(transactionNum, excludeId) > 0;
    }
    
    @Override
    public int countByTransactionType(Integer transactionType) {
        if (transactionType == null) {
            throw new BusinessException("事务类型不能为空");
        }
        return inventoryTransactionMapper.countByTransactionType(transactionType);
    }
    
    @Override
    public Integer sumQuantityByDateRangeForSpec(Long materialSpecId, Integer transactionType,
                                                LocalDateTime startDate, LocalDateTime endDate) {
        if (materialSpecId == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        if (transactionType == null) {
            throw new BusinessException("事务类型不能为空");
        }
        
        return inventoryTransactionMapper.sumQuantityByDateRangeForSpec(materialSpecId, transactionType,
                                                                       startDate, endDate);
    }
    
    @Override
    public Integer sumQuantityByDateRange(Long materialId, Integer transactionType,
                                         LocalDateTime startDate, LocalDateTime endDate) {
        if (materialId == null) {
            throw new BusinessException("物料ID不能为空");
        }
        if (transactionType == null) {
            throw new BusinessException("事务类型不能为空");
        }
        
        return inventoryTransactionMapper.sumQuantityByDateRange(materialId, transactionType,
                                                               startDate, endDate);
    }
    
    @Override
    @Transactional
    public InventoryTransaction processInbound(InventoryTransaction transaction) {
        if (transaction.getTransactionType() == null || transaction.getTransactionType() != 1) {
            throw new BusinessException("事务类型必须为入库(1)");
        }
        
        // 创建出入库记录
        InventoryTransaction createdTransaction = create(transaction);
        
        // 更新库存
        if (transaction.getMaterialSpecId() != null && transaction.getQuantity() != null) {
            inventoryService.increaseStock(transaction.getMaterialSpecId(), transaction.getQuantity());
        }
        
        log.info("处理入库业务成功，事务单号: {}, 数量: {}", transaction.getTransactionNum(), transaction.getQuantity());
        return createdTransaction;
    }
    
    @Override
    @Transactional
    public InventoryTransaction processOutbound(InventoryTransaction transaction) {
        if (transaction.getTransactionType() == null || transaction.getTransactionType() != 2) {
            throw new BusinessException("事务类型必须为出库(2)");
        }
        
        // 先检查库存是否充足
        if (transaction.getMaterialSpecId() != null && transaction.getQuantity() != null) {
            // 这里会在decreaseStock方法中检查库存是否充足
        }
        
        // 创建出入库记录
        InventoryTransaction createdTransaction = create(transaction);
        
        // 更新库存
        if (transaction.getMaterialSpecId() != null && transaction.getQuantity() != null) {
            inventoryService.decreaseStock(transaction.getMaterialSpecId(), transaction.getQuantity());
        }
        
        log.info("处理出库业务成功，事务单号: {}, 数量: {}", transaction.getTransactionNum(), transaction.getQuantity());
        return createdTransaction;
    }
    
    @Override
    @Transactional
    public void batchImport(List<InventoryTransaction> transactions) {
        if (transactions == null || transactions.isEmpty()) {
            throw new BusinessException("导入数据不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        for (InventoryTransaction transaction : transactions) {
            validateTransaction(transaction);
            
            // 检查事务单号是否已存在
            if (existsByTransactionNum(transaction.getTransactionNum())) {
                log.warn("跳过已存在的事务单号: {}", transaction.getTransactionNum());
                continue;
            }
            
            // 设置创建时间和更新时间
            transaction.setCreateTime(now);
            transaction.setUpdateTime(now);
            
            // 设置默认状态
            if (transaction.getStatus() == null) {
                transaction.setStatus(1);
            }
            
            // 设置事务日期
            if (transaction.getTransactionDate() == null) {
                transaction.setTransactionDate(now);
            }
            
            inventoryTransactionMapper.insert(transaction);
        }
        
        log.info("批量导入出入库记录完成，共处理 {} 条记录", transactions.size());
    }
    
    @Override
    public List<InventoryTransaction> exportData(String transactionNum, String eid, String specName,
                                                Integer transactionType, String orderNum, Long supplierId,
                                                String operatorId, LocalDateTime startDate, LocalDateTime endDate,
                                                Integer status) {
        // 导出时不分页，获取所有数据
        Page<InventoryTransaction> pageParam = new Page<>(1, Integer.MAX_VALUE);
        IPage<InventoryTransaction> pageResult = inventoryTransactionMapper.findByPage(pageParam, transactionNum, eid, specName,
                                                                                      transactionType, orderNum, supplierId,
                                                                                      operatorId, startDate, endDate, status);
        return pageResult.getRecords();
    }
    
    /**
     * 验证出入库记录数据
     */
    private void validateTransaction(InventoryTransaction transaction) {
        if (transaction == null) {
            throw new BusinessException("出入库记录信息不能为空");
        }
        
        if (!StringUtils.hasText(transaction.getTransactionNum())) {
            throw new BusinessException("事务单号不能为空");
        }
        
        if (transaction.getTransactionNum().length() > 50) {
            throw new BusinessException("事务单号长度不能超过50个字符");
        }
        
        if (transaction.getMaterialSpecId() == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        
        if (transaction.getTransactionType() == null) {
            throw new BusinessException("事务类型不能为空");
        }
        
        if (transaction.getTransactionType() != 1 && transaction.getTransactionType() != 2) {
            throw new BusinessException("事务类型无效，必须为1(入库)或2(出库)");
        }
        
        if (transaction.getQuantity() == null || transaction.getQuantity() <= 0) {
            throw new BusinessException("数量必须大于0");
        }
        
        if (transaction.getStatus() != null && (transaction.getStatus() < 0 || transaction.getStatus() > 1)) {
            throw new BusinessException("状态值无效，必须为0或1");
        }
    }
}
