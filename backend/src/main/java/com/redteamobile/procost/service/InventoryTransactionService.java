package com.redteamobile.procost.service;

import com.redteamobile.procost.entity.InventoryTransaction;
import com.redteamobile.procost.common.PageResult;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 出入库记录服务接口
 */
public interface InventoryTransactionService {
    
    /**
     * 根据ID查询出入库记录
     */
    InventoryTransaction findById(Long id);
    
    /**
     * 根据事务单号查询出入库记录
     */
    InventoryTransaction findByTransactionNum(String transactionNum);
    
    /**
     * 根据物料ID查询出入库记录
     */
    List<InventoryTransaction> findByMaterialId(Long materialId);
    
    /**
     * 根据物料规格ID查询出入库记录
     */
    List<InventoryTransaction> findByMaterialSpecId(Long materialSpecId);
    
    /**
     * 分页查询出入库记录
     */
    PageResult<InventoryTransaction> findByPage(String transactionNum,
                                               String eid,
                                               String specName,
                                               Integer transactionType,
                                               String orderNum,
                                               Long supplierId,
                                               String operatorId,
                                               LocalDateTime startDate,
                                               LocalDateTime endDate,
                                               Integer status,
                                               Integer page,
                                               Integer size);
    
    /**
     * 创建出入库记录
     */
    InventoryTransaction create(InventoryTransaction transaction);
    
    /**
     * 更新出入库记录
     */
    InventoryTransaction update(InventoryTransaction transaction);
    
    /**
     * 更新记录状态
     */
    void updateStatus(Long id, Integer status);
    
    /**
     * 根据ID删除出入库记录
     */
    void deleteById(Long id);
    
    /**
     * 检查事务单号是否存在
     */
    boolean existsByTransactionNum(String transactionNum);
    
    /**
     * 检查事务单号是否存在（排除指定ID）
     */
    boolean existsByTransactionNumExcludeId(String transactionNum, Long excludeId);
    
    /**
     * 根据事务类型统计数量
     */
    int countByTransactionType(Integer transactionType);
    
    /**
     * 统计指定时间范围内的出入库数量（按物料规格）
     */
    Integer sumQuantityByDateRangeForSpec(Long materialSpecId,
                                         Integer transactionType,
                                         LocalDateTime startDate,
                                         LocalDateTime endDate);
    
    /**
     * 统计指定时间范围内的出入库数量（按具体物料）
     */
    Integer sumQuantityByDateRange(Long materialId,
                                  Integer transactionType,
                                  LocalDateTime startDate,
                                  LocalDateTime endDate);
    
    /**
     * 处理入库业务
     */
    InventoryTransaction processInbound(InventoryTransaction transaction);
    
    /**
     * 处理出库业务
     */
    InventoryTransaction processOutbound(InventoryTransaction transaction);
    
    /**
     * 批量导入出入库记录
     */
    void batchImport(List<InventoryTransaction> transactions);
    
    /**
     * 导出出入库记录数据
     */
    List<InventoryTransaction> exportData(String transactionNum,
                                         String eid,
                                         String specName,
                                         Integer transactionType,
                                         String orderNum,
                                         Long supplierId,
                                         String operatorId,
                                         LocalDateTime startDate,
                                         LocalDateTime endDate,
                                         Integer status);
}
