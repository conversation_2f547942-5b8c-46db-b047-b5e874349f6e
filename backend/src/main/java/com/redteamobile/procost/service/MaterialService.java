package com.redteamobile.procost.service;

import com.redteamobile.procost.entity.Material;
import com.redteamobile.procost.common.PageResult;
import java.util.List;

/**
 * 物料服务接口
 */
public interface MaterialService {
    
    /**
     * 根据ID查询物料
     */
    Material findById(Long id);
    
    /**
     * 根据EID查询物料
     */
    Material findByEid(String eid);
    
    /**
     * 查询所有物料
     */
    List<Material> findAll();
    
    /**
     * 根据状态查询物料
     */
    List<Material> findByStatus(Integer status);
    
    /**
     * 分页查询物料
     */
    PageResult<Material> findByPage(String eid,
                                   Long materialSpecId,
                                   String category,
                                   String cardType,
                                   String environment,
                                   String manufacturer,
                                   Integer status,
                                   Integer page,
                                   Integer size);
    
    /**
     * 根据规格ID查询物料列表
     */
    List<Material> findByMaterialSpecId(Long materialSpecId);
    
    /**
     * 创建物料
     */
    Material create(Material material);
    
    /**
     * 更新物料
     */
    Material update(Material material);
    
    /**
     * 根据ID删除物料
     */
    void deleteById(Long id);
    
    /**
     * 检查EID是否存在
     */
    boolean existsByEid(String eid);
    
    /**
     * 检查EID是否存在（排除指定ID）
     */
    boolean existsByEidExcludeId(String eid, Long excludeId);
    
    /**
     * 根据状态统计物料数量
     */
    int countByStatus(Integer status);
    
    /**
     * 批量导入物料
     */
    void batchImport(List<Material> materials);
    
    /**
     * 导出物料数据
     */
    List<Material> exportData(String eid,
                             Long materialSpecId,
                             String category,
                             String cardType,
                             String environment,
                             String manufacturer,
                             Integer status);
}
