package com.redteamobile.procost.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redteamobile.procost.entity.MaterialSpec;
import com.redteamobile.procost.mapper.MaterialSpecMapper;
import com.redteamobile.procost.service.MaterialSpecService;
import com.redteamobile.procost.common.PageResult;
import com.redteamobile.procost.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物料规格服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialSpecServiceImpl implements MaterialSpecService {
    
    private final MaterialSpecMapper materialSpecMapper;
    
    @Override
    public MaterialSpec findById(Long id) {
        if (id == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        MaterialSpec materialSpec = materialSpecMapper.selectById(id);
        if (materialSpec == null) {
            throw new BusinessException("物料规格不存在");
        }
        return materialSpec;
    }
    
    @Override
    public MaterialSpec findBySpecCode(String specCode) {
        if (!StringUtils.hasText(specCode)) {
            throw new BusinessException("规格编码不能为空");
        }
        return materialSpecMapper.findBySpecCode(specCode);
    }
    
    @Override
    public List<MaterialSpec> findAll() {
        return materialSpecMapper.selectList(null);
    }
    
    @Override
    public List<MaterialSpec> findByStatus(Integer status) {
        if (status == null) {
            throw new BusinessException("状态不能为空");
        }
        return materialSpecMapper.findByStatus(status);
    }
    
    @Override
    public PageResult<MaterialSpec> findByPage(String specCode, String specName, String category,
                                              String cardType, String environment, String manufacturer,
                                              Integer status, Integer page, Integer size) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        // 使用 MyBatis-Plus 分页
        Page<MaterialSpec> pageParam = new Page<>(page, size);
        IPage<MaterialSpec> pageResult = materialSpecMapper.findByPage(pageParam, specCode, specName, category,
                                                                      cardType, environment, manufacturer, status);
        
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal(), page, size);
    }
    
    @Override
    public List<MaterialSpec> findByCategory(String category) {
        if (!StringUtils.hasText(category)) {
            throw new BusinessException("类别不能为空");
        }
        return materialSpecMapper.findByCategory(category);
    }
    
    @Override
    public List<MaterialSpec> findByManufacturer(String manufacturer) {
        if (!StringUtils.hasText(manufacturer)) {
            throw new BusinessException("制造商不能为空");
        }
        return materialSpecMapper.findByManufacturer(manufacturer);
    }
    
    @Override
    @Transactional
    public MaterialSpec create(MaterialSpec materialSpec) {
        validateMaterialSpec(materialSpec);
        
        // 检查规格编码是否已存在
        if (existsBySpecCode(materialSpec.getSpecCode())) {
            throw new BusinessException("规格编码已存在: " + materialSpec.getSpecCode());
        }
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        materialSpec.setCreateTime(now);
        materialSpec.setUpdateTime(now);
        
        // 设置默认状态
        if (materialSpec.getStatus() == null) {
            materialSpec.setStatus(1);
        }
        
        // 设置默认单位
        if (!StringUtils.hasText(materialSpec.getUnit())) {
            materialSpec.setUnit("张");
        }
        
        int result = materialSpecMapper.insert(materialSpec);
        if (result <= 0) {
            throw new BusinessException("创建物料规格失败");
        }
        
        log.info("创建物料规格成功，ID: {}, 规格编码: {}", materialSpec.getId(), materialSpec.getSpecCode());
        return materialSpec;
    }
    
    @Override
    @Transactional
    public MaterialSpec update(MaterialSpec materialSpec) {
        if (materialSpec.getId() == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        
        // 检查物料规格是否存在
        MaterialSpec existingSpec = findById(materialSpec.getId());
        
        validateMaterialSpec(materialSpec);
        
        // 检查规格编码是否已被其他规格使用
        if (existsBySpecCodeExcludeId(materialSpec.getSpecCode(), materialSpec.getId())) {
            throw new BusinessException("规格编码已被其他规格使用: " + materialSpec.getSpecCode());
        }
        
        // 设置更新时间
        materialSpec.setUpdateTime(LocalDateTime.now());
        
        int result = materialSpecMapper.update(new UpdateWrapper<>(materialSpec));
        if (result <= 0) {
            throw new BusinessException("更新物料规格失败");
        }
        
        log.info("更新物料规格成功，ID: {}, 规格编码: {}", materialSpec.getId(), materialSpec.getSpecCode());
        return findById(materialSpec.getId());
    }
    
    @Override
    @Transactional
    public void deleteById(Long id) {
        if (id == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        
        // 检查物料规格是否存在
        MaterialSpec materialSpec = findById(id);
        
        // TODO: 检查是否有相关的物料记录
        
        int result = materialSpecMapper.deleteById(id);
        if (result <= 0) {
            throw new BusinessException("删除物料规格失败");
        }
        
        log.info("删除物料规格成功，ID: {}, 规格编码: {}", id, materialSpec.getSpecCode());
    }
    
    @Override
    public boolean existsBySpecCode(String specCode) {
        if (!StringUtils.hasText(specCode)) {
            return false;
        }
        return materialSpecMapper.existsBySpecCode(specCode) > 0;
    }
    
    @Override
    public boolean existsBySpecCodeExcludeId(String specCode, Long excludeId) {
        if (!StringUtils.hasText(specCode) || excludeId == null) {
            return false;
        }
        return materialSpecMapper.existsBySpecCodeExcludeId(specCode, excludeId) > 0;
    }
    
    @Override
    public int countByStatus(Integer status) {
        if (status == null) {
            throw new BusinessException("状态不能为空");
        }
        return materialSpecMapper.countByStatus(status);
    }
    
    @Override
    public int countByCategory(String category) {
        if (!StringUtils.hasText(category)) {
            throw new BusinessException("类别不能为空");
        }
        return materialSpecMapper.countByCategory(category);
    }
    
    @Override
    @Transactional
    public void batchImport(List<MaterialSpec> materialSpecs) {
        if (materialSpecs == null || materialSpecs.isEmpty()) {
            throw new BusinessException("导入数据不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        for (MaterialSpec materialSpec : materialSpecs) {
            validateMaterialSpec(materialSpec);
            
            // 检查规格编码是否已存在
            if (existsBySpecCode(materialSpec.getSpecCode())) {
                log.warn("跳过已存在的规格编码: {}", materialSpec.getSpecCode());
                continue;
            }
            
            // 设置创建时间和更新时间
            materialSpec.setCreateTime(now);
            materialSpec.setUpdateTime(now);
            
            // 设置默认状态
            if (materialSpec.getStatus() == null) {
                materialSpec.setStatus(1);
            }
            
            // 设置默认单位
            if (!StringUtils.hasText(materialSpec.getUnit())) {
                materialSpec.setUnit("张");
            }
            
            materialSpecMapper.insert(materialSpec);
        }
        
        log.info("批量导入物料规格完成，共处理 {} 条记录", materialSpecs.size());
    }
    
    @Override
    public List<MaterialSpec> exportData(String specCode, String specName, String category,
                                        String cardType, String environment, String manufacturer,
                                        Integer status) {
        // 导出时不分页，获取所有数据
        Page<MaterialSpec> pageParam = new Page<>(1, Integer.MAX_VALUE);
        IPage<MaterialSpec> pageResult = materialSpecMapper.findByPage(pageParam, specCode, specName, category,
                                                                      cardType, environment, manufacturer, status);
        return pageResult.getRecords();
    }
    
    /**
     * 验证物料规格数据
     */
    private void validateMaterialSpec(MaterialSpec materialSpec) {
        if (materialSpec == null) {
            throw new BusinessException("物料规格信息不能为空");
        }
        
        if (!StringUtils.hasText(materialSpec.getSpecCode())) {
            throw new BusinessException("规格编码不能为空");
        }
        
        if (materialSpec.getSpecCode().length() > 50) {
            throw new BusinessException("规格编码长度不能超过50个字符");
        }
        
        if (!StringUtils.hasText(materialSpec.getSpecName())) {
            throw new BusinessException("规格名称不能为空");
        }
        
        if (materialSpec.getSpecName().length() > 100) {
            throw new BusinessException("规格名称长度不能超过100个字符");
        }
        
        if (materialSpec.getStatus() != null && (materialSpec.getStatus() < 0 || materialSpec.getStatus() > 1)) {
            throw new BusinessException("状态值无效，必须为0或1");
        }
    }
}
