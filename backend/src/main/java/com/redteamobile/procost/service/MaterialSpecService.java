package com.redteamobile.procost.service;

import com.redteamobile.procost.entity.MaterialSpec;
import com.redteamobile.procost.common.PageResult;
import java.util.List;

/**
 * 物料规格服务接口
 */
public interface MaterialSpecService {
    
    /**
     * 根据ID查询物料规格
     */
    MaterialSpec findById(Long id);
    
    /**
     * 根据规格编码查询物料规格
     */
    MaterialSpec findBySpecCode(String specCode);
    
    /**
     * 查询所有物料规格
     */
    List<MaterialSpec> findAll();
    
    /**
     * 根据状态查询物料规格
     */
    List<MaterialSpec> findByStatus(Integer status);
    
    /**
     * 分页查询物料规格
     */
    PageResult<MaterialSpec> findByPage(String specCode,
                                       String specName,
                                       String category,
                                       String cardType,
                                       String environment,
                                       String manufacturer,
                                       Integer status,
                                       Integer page,
                                       Integer size);
    
    /**
     * 根据类别查询物料规格
     */
    List<MaterialSpec> findByCategory(String category);
    
    /**
     * 根据制造商查询物料规格
     */
    List<MaterialSpec> findByManufacturer(String manufacturer);
    
    /**
     * 创建物料规格
     */
    MaterialSpec create(MaterialSpec materialSpec);
    
    /**
     * 更新物料规格
     */
    MaterialSpec update(MaterialSpec materialSpec);
    
    /**
     * 根据ID删除物料规格
     */
    void deleteById(Long id);
    
    /**
     * 检查规格编码是否存在
     */
    boolean existsBySpecCode(String specCode);
    
    /**
     * 检查规格编码是否存在（排除指定ID）
     */
    boolean existsBySpecCodeExcludeId(String specCode, Long excludeId);
    
    /**
     * 根据状态统计物料规格数量
     */
    int countByStatus(Integer status);
    
    /**
     * 根据类别统计物料规格数量
     */
    int countByCategory(String category);
    
    /**
     * 批量导入物料规格
     */
    void batchImport(List<MaterialSpec> materialSpecs);
    
    /**
     * 导出物料规格数据
     */
    List<MaterialSpec> exportData(String specCode,
                                 String specName,
                                 String category,
                                 String cardType,
                                 String environment,
                                 String manufacturer,
                                 Integer status);
}
