package com.redteamobile.procost.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redteamobile.procost.entity.Supplier;
import com.redteamobile.procost.mapper.SupplierMapper;
import com.redteamobile.procost.service.SupplierService;
import com.redteamobile.procost.common.PageResult;
import com.redteamobile.procost.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierServiceImpl implements SupplierService {
    
    private final SupplierMapper supplierMapper;
    
    @Override
    public Supplier findById(Long id) {
        if (id == null) {
            throw new BusinessException("供应商ID不能为空");
        }
        Supplier supplier = supplierMapper.findById(id);
        if (supplier == null) {
            throw new BusinessException("供应商不存在");
        }
        return supplier;
    }
    
    @Override
    public Supplier findBySupplierCode(String supplierCode) {
        if (!StringUtils.hasText(supplierCode)) {
            throw new BusinessException("供应商编码不能为空");
        }
        return supplierMapper.findBySupplierCode(supplierCode);
    }
    
    @Override
    public List<Supplier> findAll() {
        return supplierMapper.findAll();
    }
    
    @Override
    public List<Supplier> findByStatus(Integer status) {
        if (status == null) {
            throw new BusinessException("状态不能为空");
        }
        return supplierMapper.findByStatus(status);
    }
    
    @Override
    public PageResult<Supplier> findByPage(String supplierCode, String supplierName, 
                                          String address, Integer status, 
                                          Integer page, Integer size) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        // 使用 MyBatis-Plus 分页
        Page<Supplier> pageParam = new Page<>(page, size);
        IPage<Supplier> pageResult = supplierMapper.findByPage(pageParam, supplierCode, supplierName, 
                                                              address, status);
        
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal(), page, size);
    }
    
    @Override
    @Transactional
    public Supplier create(Supplier supplier) {
        validateSupplier(supplier);
        
        // 检查供应商编码是否已存在
        if (existsBySupplierCode(supplier.getSupplierCode())) {
            throw new BusinessException("供应商编码已存在: " + supplier.getSupplierCode());
        }
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        supplier.setCreateTime(now);
        supplier.setUpdateTime(now);
        
        // 设置默认状态
        if (supplier.getStatus() == null) {
            supplier.setStatus(1);
        }
        
        int result = supplierMapper.insert(supplier);
        if (result <= 0) {
            throw new BusinessException("创建供应商失败");
        }
        
        log.info("创建供应商成功，ID: {}, 编码: {}", supplier.getId(), supplier.getSupplierCode());
        return supplier;
    }
    
    @Override
    @Transactional
    public Supplier update(Supplier supplier) {
        if (supplier.getId() == null) {
            throw new BusinessException("供应商ID不能为空");
        }
        
        // 检查供应商是否存在
        Supplier existingSupplier = findById(supplier.getId());
        
        validateSupplier(supplier);
        
        // 检查供应商编码是否已被其他供应商使用
        if (existsBySupplierCodeExcludeId(supplier.getSupplierCode(), supplier.getId())) {
            throw new BusinessException("供应商编码已被其他供应商使用: " + supplier.getSupplierCode());
        }
        
        // 设置更新时间
        supplier.setUpdateTime(LocalDateTime.now());
        
        int result = supplierMapper.update(supplier);
        if (result <= 0) {
            throw new BusinessException("更新供应商失败");
        }
        
        log.info("更新供应商成功，ID: {}, 编码: {}", supplier.getId(), supplier.getSupplierCode());
        return findById(supplier.getId());
    }
    
    @Override
    @Transactional
    public void deleteById(Long id) {
        if (id == null) {
            throw new BusinessException("供应商ID不能为空");
        }
        
        // 检查供应商是否存在
        Supplier supplier = findById(id);
        
        // TODO: 检查是否有相关的出入库记录
        
        int result = supplierMapper.deleteById(id);
        if (result <= 0) {
            throw new BusinessException("删除供应商失败");
        }
        
        log.info("删除供应商成功，ID: {}, 编码: {}", id, supplier.getSupplierCode());
    }
    
    @Override
    public boolean existsBySupplierCode(String supplierCode) {
        if (!StringUtils.hasText(supplierCode)) {
            return false;
        }
        return supplierMapper.existsBySupplierCode(supplierCode) > 0;
    }
    
    @Override
    public boolean existsBySupplierCodeExcludeId(String supplierCode, Long excludeId) {
        if (!StringUtils.hasText(supplierCode) || excludeId == null) {
            return false;
        }
        return supplierMapper.existsBySupplierCodeExcludeId(supplierCode, excludeId) > 0;
    }
    
    @Override
    public int countByStatus(Integer status) {
        if (status == null) {
            throw new BusinessException("状态不能为空");
        }
        return supplierMapper.countByStatus(status);
    }
    
    @Override
    @Transactional
    public void batchImport(List<Supplier> suppliers) {
        if (suppliers == null || suppliers.isEmpty()) {
            throw new BusinessException("导入数据不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        for (Supplier supplier : suppliers) {
            validateSupplier(supplier);
            
            // 检查供应商编码是否已存在
            if (existsBySupplierCode(supplier.getSupplierCode())) {
                log.warn("跳过已存在的供应商编码: {}", supplier.getSupplierCode());
                continue;
            }
            
            // 设置创建时间和更新时间
            supplier.setCreateTime(now);
            supplier.setUpdateTime(now);
            
            // 设置默认状态
            if (supplier.getStatus() == null) {
                supplier.setStatus(1);
            }
            
            supplierMapper.insert(supplier);
        }
        
        log.info("批量导入供应商完成，共处理 {} 条记录", suppliers.size());
    }
    
    @Override
    public List<Supplier> exportData(String supplierCode, String supplierName, 
                                   String address, Integer status) {
        // 导出时不分页，获取所有数据
        Page<Supplier> pageParam = new Page<>(1, Integer.MAX_VALUE);
        IPage<Supplier> pageResult = supplierMapper.findByPage(pageParam, supplierCode, supplierName, 
                                                              address, status);
        return pageResult.getRecords();
    }
    
    /**
     * 验证供应商数据
     */
    private void validateSupplier(Supplier supplier) {
        if (supplier == null) {
            throw new BusinessException("供应商信息不能为空");
        }
        
        if (!StringUtils.hasText(supplier.getSupplierCode())) {
            throw new BusinessException("供应商编码不能为空");
        }
        
        if (supplier.getSupplierCode().length() > 50) {
            throw new BusinessException("供应商编码长度不能超过50个字符");
        }
        
        if (!StringUtils.hasText(supplier.getSupplierName())) {
            throw new BusinessException("供应商名称不能为空");
        }
        
        if (supplier.getSupplierName().length() > 100) {
            throw new BusinessException("供应商名称长度不能超过100个字符");
        }
        
        if (supplier.getStatus() != null && (supplier.getStatus() < 0 || supplier.getStatus() > 1)) {
            throw new BusinessException("状态值无效，必须为0或1");
        }
    }
}
