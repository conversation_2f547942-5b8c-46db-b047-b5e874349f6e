package com.redteamobile.procost.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redteamobile.procost.entity.Material;
import com.redteamobile.procost.mapper.MaterialMapper;
import com.redteamobile.procost.service.MaterialService;
import com.redteamobile.procost.common.PageResult;
import com.redteamobile.procost.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物料服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialServiceImpl implements MaterialService {
    
    private final MaterialMapper materialMapper;
    
    @Override
    public Material findById(Long id) {
        if (id == null) {
            throw new BusinessException("物料ID不能为空");
        }
        Material material = materialMapper.selectById(id);
        if (material == null) {
            throw new BusinessException("物料不存在");
        }
        return material;
    }
    
    @Override
    public Material findByEid(String eid) {
        if (!StringUtils.hasText(eid)) {
            throw new BusinessException("EID不能为空");
        }
        return materialMapper.findByEid(eid);
    }
    
    @Override
    public List<Material> findAll() {
        return materialMapper.findAllWithSpec();
    }
    
    @Override
    public List<Material> findByStatus(Integer status) {
        if (status == null) {
            throw new BusinessException("状态不能为空");
        }
        return materialMapper.findByStatus(status);
    }
    
    @Override
    public PageResult<Material> findByPage(String eid, Long materialSpecId, String category, 
                                          String cardType, String environment, String manufacturer, 
                                          Integer status, Integer page, Integer size) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        // 使用 MyBatis-Plus 分页
        Page<Material> pageParam = new Page<>(page, size);
        IPage<Material> pageResult = materialMapper.findByPage(pageParam, eid, materialSpecId, category, 
                                                              cardType, environment, manufacturer, status);
        
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal(), page, size);
    }
    
    @Override
    public List<Material> findByMaterialSpecId(Long materialSpecId) {
        if (materialSpecId == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        return materialMapper.findByMaterialSpecId(materialSpecId);
    }
    
    @Override
    @Transactional
    public Material create(Material material) {
        validateMaterial(material);
        
        // 检查EID是否已存在
        if (existsByEid(material.getEid())) {
            throw new BusinessException("EID已存在: " + material.getEid());
        }
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        material.setCreateTime(now);
        material.setUpdateTime(now);
        
        // 设置默认状态
        if (material.getStatus() == null) {
            material.setStatus(1);
        }
        
        int result = materialMapper.insert(material);
        if (result <= 0) {
            throw new BusinessException("创建物料失败");
        }
        
        log.info("创建物料成功，ID: {}, EID: {}", material.getId(), material.getEid());
        return material;
    }
    
    @Override
    @Transactional
    public Material update(Material material) {
        if (material.getId() == null) {
            throw new BusinessException("物料ID不能为空");
        }
        
        // 检查物料是否存在
        Material existingMaterial = findById(material.getId());
        
        validateMaterial(material);
        
        // 检查EID是否已被其他物料使用
        if (existsByEidExcludeId(material.getEid(), material.getId())) {
            throw new BusinessException("EID已被其他物料使用: " + material.getEid());
        }
        
        // 设置更新时间
        material.setUpdateTime(LocalDateTime.now());
        
        int result = materialMapper.update(new UpdateWrapper<>(material));
        if (result <= 0) {
            throw new BusinessException("更新物料失败");
        }
        
        log.info("更新物料成功，ID: {}, EID: {}", material.getId(), material.getEid());
        return findById(material.getId());
    }
    
    @Override
    @Transactional
    public void deleteById(Long id) {
        if (id == null) {
            throw new BusinessException("物料ID不能为空");
        }
        
        // 检查物料是否存在
        Material material = findById(id);
        
        // TODO: 检查是否有相关的库存记录或出入库记录
        
        int result = materialMapper.deleteById(id);
        if (result <= 0) {
            throw new BusinessException("删除物料失败");
        }
        
        log.info("删除物料成功，ID: {}, EID: {}", id, material.getEid());
    }
    
    @Override
    public boolean existsByEid(String eid) {
        if (!StringUtils.hasText(eid)) {
            return false;
        }
        return materialMapper.existsByEid(eid) > 0;
    }
    
    @Override
    public boolean existsByEidExcludeId(String eid, Long excludeId) {
        if (!StringUtils.hasText(eid) || excludeId == null) {
            return false;
        }
        return materialMapper.existsByEidExcludeId(eid, excludeId) > 0;
    }
    
    @Override
    public int countByStatus(Integer status) {
        if (status == null) {
            throw new BusinessException("状态不能为空");
        }
        return materialMapper.countByStatus(status);
    }
    
    @Override
    @Transactional
    public void batchImport(List<Material> materials) {
        if (materials == null || materials.isEmpty()) {
            throw new BusinessException("导入数据不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        for (Material material : materials) {
            validateMaterial(material);
            
            // 检查EID是否已存在
            if (existsByEid(material.getEid())) {
                log.warn("跳过已存在的EID: {}", material.getEid());
                continue;
            }
            
            // 设置创建时间和更新时间
            material.setCreateTime(now);
            material.setUpdateTime(now);
            
            // 设置默认状态
            if (material.getStatus() == null) {
                material.setStatus(1);
            }
            
            materialMapper.insert(material);
        }
        
        log.info("批量导入物料完成，共处理 {} 条记录", materials.size());
    }
    
    @Override
    public List<Material> exportData(String eid, Long materialSpecId, String category, 
                                   String cardType, String environment, String manufacturer, 
                                   Integer status) {
        // 导出时不分页，获取所有数据
        Page<Material> pageParam = new Page<>(1, Integer.MAX_VALUE);
        IPage<Material> pageResult = materialMapper.findByPage(pageParam, eid, materialSpecId, category, 
                                                              cardType, environment, manufacturer, status);
        return pageResult.getRecords();
    }
    
    /**
     * 验证物料数据
     */
    private void validateMaterial(Material material) {
        if (material == null) {
            throw new BusinessException("物料信息不能为空");
        }
        
        if (!StringUtils.hasText(material.getEid())) {
            throw new BusinessException("EID不能为空");
        }
        
        if (material.getEid().length() > 50) {
            throw new BusinessException("EID长度不能超过50个字符");
        }
        
        if (material.getMaterialSpecId() == null) {
            throw new BusinessException("物料规格ID不能为空");
        }
        
        if (material.getStatus() != null && (material.getStatus() < 0 || material.getStatus() > 1)) {
            throw new BusinessException("状态值无效，必须为0或1");
        }
    }
}
