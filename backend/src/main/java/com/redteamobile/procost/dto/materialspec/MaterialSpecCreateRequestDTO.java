package com.redteamobile.procost.dto.materialspec;

import com.redteamobile.procost.dto.BaseRequestDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物料规格创建请求 DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialSpecCreateRequestDTO extends BaseRequestDTO {
    
    /**
     * 规格编码
     */
    @NotBlank(message = "规格编码不能为空")
    private String specCode;
    
    /**
     * 规格名称
     */
    @NotBlank(message = "规格名称不能为空")
    private String specName;
    
    /**
     * 类别
     */
    private String category;
    
    /**
     * 卡片类型
     */
    private String cardType;
    
    /**
     * 卡片等级
     */
    private String cardLevel;
    
    /**
     * 环境
     */
    private String environment;
    
    /**
     * 制造商
     */
    private String manufacturer;
    
    /**
     * 配置
     */
    private String configuration;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 规格说明
     */
    private String specification;
    
    /**
     * 状态：0-停用，1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status = 1;
}
