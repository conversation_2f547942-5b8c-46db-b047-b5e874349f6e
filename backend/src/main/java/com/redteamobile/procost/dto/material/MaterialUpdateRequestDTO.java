package com.redteamobile.procost.dto.material;

import com.redteamobile.procost.dto.BaseRequestDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物料更新请求 DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialUpdateRequestDTO extends BaseRequestDTO {
    
    /**
     * EID号
     */
    @NotBlank(message = "EID不能为空")
    private String eid;
    
    /**
     * 物料规格ID
     */
    @NotNull(message = "物料规格ID不能为空")
    private Long materialSpecId;
    
    /**
     * 状态：0-停用，1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}
