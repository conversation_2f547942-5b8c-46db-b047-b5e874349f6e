package com.redteamobile.procost.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 基础响应 DTO
 * 只包含真正通用的审计字段
 */
@Data
public abstract class BaseResponseDTO {
    
    /**
     * ID
     */
    private Long id;
    
    /**
     * 创建人（Keycloak用户ID）
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新人（Keycloak用户ID）
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
}
