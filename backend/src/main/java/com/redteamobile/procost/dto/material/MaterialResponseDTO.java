package com.redteamobile.procost.dto.material;

import com.redteamobile.procost.dto.BaseResponseDTO;
import com.redteamobile.procost.dto.materialspec.MaterialSpecResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物料响应 DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialResponseDTO extends BaseResponseDTO {
    
    /**
     * EID号
     */
    private String eid;
    
    /**
     * 物料规格ID
     */
    private Long materialSpecId;
    
    /**
     * 状态：0-停用，1-启用
     */
    private Integer status;
    
    /**
     * 关联的物料规格信息
     */
    private MaterialSpecResponseDTO materialSpec;
}
