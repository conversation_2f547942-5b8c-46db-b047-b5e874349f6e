package com.redteamobile.procost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redteamobile.procost.entity.Inventory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存数据访问层接口
 */
@Mapper
public interface InventoryMapper extends BaseMapper<Inventory> {
    
    /**
     * 根据物料规格ID查询库存
     */
    Inventory findByMaterialSpecId(@Param("materialSpecId") Long materialSpecId);
    
    /**
     * 查询所有库存（带规格信息）
     */
    List<Inventory> findAllWithSpec();
    
    /**
     * 多条件分页查询库存
     */
    IPage<Inventory> findByPage(Page<Inventory> page,
                               @Param("materialSpecId") Long materialSpecId,
                               @Param("category") String category,
                               @Param("cardType") String cardType,
                               @Param("environment") String environment,
                               @Param("manufacturer") String manufacturer,
                               @Param("minStock") Integer minStock,
                               @Param("maxStock") Integer maxStock);
    
    /**
     * 查询库存预警列表（当前库存 <= 最小库存预警）
     */
    List<Inventory> findLowStockInventories();
    
    /**
     * 更新库存数量
     */
    int updateStock(@Param("materialSpecId") Long materialSpecId,
                    @Param("currentStock") Integer currentStock,
                    @Param("availableStock") Integer availableStock,
                    @Param("reservedStock") Integer reservedStock);
    
    /**
     * 更新最后入库时间
     */
    int updateLastInTime(@Param("materialSpecId") Long materialSpecId,
                        @Param("lastInTime") LocalDateTime lastInTime);
    
    /**
     * 更新最后出库时间
     */
    int updateLastOutTime(@Param("materialSpecId") Long materialSpecId,
                         @Param("lastOutTime") LocalDateTime lastOutTime);
    
    /**
     * 根据物料规格ID删除库存
     */
    int deleteByMaterialSpecId(@Param("materialSpecId") Long materialSpecId);
    
    /**
     * 统计库存总数
     */
    Long countAll();
    
    /**
     * 统计预警库存数量
     */
    Long countLowStock();
}