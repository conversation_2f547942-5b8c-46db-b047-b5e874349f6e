package com.redteamobile.procost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redteamobile.procost.entity.Material;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料数据访问层接口
 */
@Mapper
public interface MaterialMapper extends BaseMapper<Material> {
    
    /**
     * 根据EID号查询物料
     */
    Material findByEid(@Param("eid") String eid);
    
    /**
     * 查询所有物料（带规格信息）
     */
    List<Material> findAllWithSpec();
    
    /**
     * 根据状态查询物料
     */
    List<Material> findByStatus(@Param("status") Integer status);
    
    /**
     * 多条件分页查询物料
     */
    IPage<Material> findByPage(Page<Material> page,
                              @Param("eid") String eid,
                              @Param("materialSpecId") Long materialSpecId,
                              @Param("category") String category,
                              @Param("cardType") String cardType,
                              @Param("environment") String environment,
                              @Param("manufacturer") String manufacturer,
                              @Param("status") Integer status);
    
    /**
     * 检查EID号是否存在（排除指定ID）
     */
    int existsByEidExcludeId(@Param("eid") String eid, @Param("excludeId") Long excludeId);
    
    /**
     * 检查EID号是否存在
     */
    int existsByEid(@Param("eid") String eid);
    
    /**
     * 根据状态统计物料数量
     */
    int countByStatus(@Param("status") Integer status);
    
    /**
     * 根据规格ID查询物料列表
     */
    List<Material> findByMaterialSpecId(@Param("materialSpecId") Long materialSpecId);
}