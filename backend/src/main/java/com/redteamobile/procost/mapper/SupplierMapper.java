package com.redteamobile.procost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redteamobile.procost.entity.Supplier;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商数据访问层接口
 */
@Mapper
public interface SupplierMapper extends BaseMapper<Supplier> {
    
    /**
     * 根据供应商编码查询供应商
     */
    Supplier findBySupplierCode(@Param("supplierCode") String supplierCode);
    
    /**
     * 根据状态查询供应商
     */
    List<Supplier> findByStatus(@Param("status") Integer status);
    
    /**
     * 多条件分页查询供应商
     */
    IPage<Supplier> findByPage(Page<Supplier> page,
                              @Param("supplierCode") String supplierCode,
                              @Param("supplierName") String supplierName,
                              @Param("address") String address,
                              @Param("status") Integer status);
    
    /**
     * 检查供应商编码是否存在
     */
    int existsBySupplierCode(@Param("supplierCode") String supplierCode);
    
    /**
     * 检查供应商编码是否存在（排除指定ID）
     */
    int existsBySupplierCodeExcludeId(@Param("supplierCode") String supplierCode, @Param("excludeId") Long excludeId);
    
    /**
     * 根据状态统计供应商数量
     */
    int countByStatus(@Param("status") Integer status);
}