package com.redteamobile.procost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redteamobile.procost.entity.MaterialSpec;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料规格数据访问层接口
 */
@Mapper
public interface MaterialSpecMapper extends BaseMapper<MaterialSpec> {
    
    /**
     * 根据规格编码查询物料规格
     */
    MaterialSpec findBySpecCode(@Param("specCode") String specCode);
    
    /**
     * 根据状态查询物料规格
     */
    List<MaterialSpec> findByStatus(@Param("status") Integer status);
    
    /**
     * 多条件分页查询物料规格
     */
    IPage<MaterialSpec> findByPage(Page<MaterialSpec> page,
                                  @Param("specCode") String specCode,
                                  @Param("specName") String specName,
                                  @Param("category") String category,
                                  @Param("cardType") String cardType,
                                  @Param("environment") String environment,
                                  @Param("manufacturer") String manufacturer,
                                  @Param("status") Integer status);
    
    /**
     * 根据类别查询物料规格
     */
    List<MaterialSpec> findByCategory(@Param("category") String category);
    
    /**
     * 根据制造商查询物料规格
     */
    List<MaterialSpec> findByManufacturer(@Param("manufacturer") String manufacturer);
    
    /**
     * 检查规格编码是否存在
     */
    int existsBySpecCode(@Param("specCode") String specCode);
    
    /**
     * 检查规格编码是否存在（排除指定ID）
     */
    int existsBySpecCodeExcludeId(@Param("specCode") String specCode, @Param("excludeId") Long excludeId);
    
    /**
     * 根据状态统计物料规格数量
     */
    int countByStatus(@Param("status") Integer status);
    
    /**
     * 根据类别统计物料规格数量
     */
    int countByCategory(@Param("category") String category);
}