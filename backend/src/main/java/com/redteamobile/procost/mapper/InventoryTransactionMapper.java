package com.redteamobile.procost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redteamobile.procost.entity.InventoryTransaction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 出入库记录数据访问层接口
 */
@Mapper
public interface InventoryTransactionMapper extends BaseMapper<InventoryTransaction> {
    
    /**
     * 根据事务单号查询出入库记录
     */
    InventoryTransaction findByTransactionNum(@Param("transactionNum") String transactionNum);
    
    /**
     * 根据物料ID查询出入库记录
     */
    List<InventoryTransaction> findByMaterialId(@Param("materialId") Long materialId);
    
    /**
     * 多条件分页查询出入库记录
     */
    IPage<InventoryTransaction> findByPage(Page<InventoryTransaction> page,
                                          @Param("transactionNum") String transactionNum,
                                          @Param("eid") String eid,
                                          @Param("specName") String specName,
                                          @Param("transactionType") Integer transactionType,
                                          @Param("orderNum") String orderNum,
                                          @Param("supplierId") Long supplierId,
                                          @Param("operatorId") String operatorId,
                                          @Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate,
                                          @Param("status") Integer status);
    
    /**
     * 更新记录状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 检查事务单号是否存在
     */
    int existsByTransactionNum(@Param("transactionNum") String transactionNum);
    
    /**
     * 检查事务单号是否存在（排除指定ID）
     */
    int existsByTransactionNumExcludeId(@Param("transactionNum") String transactionNum, @Param("excludeId") Long excludeId);
    
    /**
     * 根据事务类型统计数量
     */
    int countByTransactionType(@Param("transactionType") Integer transactionType);
    
    /**
     * 统计指定时间范围内的出入库数量（按物料规格）
     */
    Integer sumQuantityByDateRangeForSpec(@Param("materialSpecId") Long materialSpecId,
                                         @Param("transactionType") Integer transactionType,
                                         @Param("startDate") LocalDateTime startDate,
                                         @Param("endDate") LocalDateTime endDate);
    
    /**
     * 统计指定时间范围内的出入库数量（按具体物料）
     */
    Integer sumQuantityByDateRange(@Param("materialId") Long materialId,
                                  @Param("transactionType") Integer transactionType,
                                  @Param("startDate") LocalDateTime startDate,
                                  @Param("endDate") LocalDateTime endDate);
    
    /**
     * 根据物料规格ID查询出入库记录
     */
    List<InventoryTransaction> findByMaterialSpecId(@Param("materialSpecId") Long materialSpecId);
}