package com.redteamobile.procost.converter;

import com.redteamobile.procost.dto.materialspec.MaterialSpecCreateRequestDTO;
import com.redteamobile.procost.dto.materialspec.MaterialSpecResponseDTO;
import com.redteamobile.procost.dto.materialspec.MaterialSpecUpdateRequestDTO;
import com.redteamobile.procost.entity.MaterialSpec;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MaterialSpec DTO 转换器
 */
@Component
public class MaterialSpecConverter {
    
    /**
     * CreateRequestDTO -> Entity
     */
    public MaterialSpec toEntity(MaterialSpecCreateRequestDTO dto) {
        if (dto == null) {
            return null;
        }
        
        MaterialSpec entity = new MaterialSpec();
        entity.setSpecCode(dto.getSpecCode());
        entity.setSpecName(dto.getSpecName());
        entity.setCategory(dto.getCategory());
        entity.setCardType(dto.getCardType());
        entity.setCardLevel(dto.getCardLevel());
        entity.setEnvironment(dto.getEnvironment());
        entity.setManufacturer(dto.getManufacturer());
        entity.setConfiguration(dto.getConfiguration());
        entity.setUnit(dto.getUnit());
        entity.setSpecification(dto.getSpecification());
        entity.setRemark(dto.getRemark());
        entity.setStatus(dto.getStatus());
        entity.setCreateBy(dto.getCreateBy());
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        
        return entity;
    }
    
    /**
     * UpdateRequestDTO -> Entity
     */
    public MaterialSpec toEntity(MaterialSpecUpdateRequestDTO dto, Long id) {
        if (dto == null) {
            return null;
        }
        
        MaterialSpec entity = new MaterialSpec();
        entity.setId(id);
        entity.setSpecCode(dto.getSpecCode());
        entity.setSpecName(dto.getSpecName());
        entity.setCategory(dto.getCategory());
        entity.setCardType(dto.getCardType());
        entity.setCardLevel(dto.getCardLevel());
        entity.setEnvironment(dto.getEnvironment());
        entity.setManufacturer(dto.getManufacturer());
        entity.setConfiguration(dto.getConfiguration());
        entity.setUnit(dto.getUnit());
        entity.setSpecification(dto.getSpecification());
        entity.setRemark(dto.getRemark());
        entity.setStatus(dto.getStatus());
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setUpdateTime(LocalDateTime.now());
        
        return entity;
    }
    
    /**
     * Entity -> ResponseDTO
     */
    public MaterialSpecResponseDTO toResponseDTO(MaterialSpec entity) {
        if (entity == null) {
            return null;
        }
        
        MaterialSpecResponseDTO dto = new MaterialSpecResponseDTO();
        dto.setId(entity.getId());
        dto.setSpecCode(entity.getSpecCode());
        dto.setSpecName(entity.getSpecName());
        dto.setCategory(entity.getCategory());
        dto.setCardType(entity.getCardType());
        dto.setCardLevel(entity.getCardLevel());
        dto.setEnvironment(entity.getEnvironment());
        dto.setManufacturer(entity.getManufacturer());
        dto.setConfiguration(entity.getConfiguration());
        dto.setUnit(entity.getUnit());
        dto.setSpecification(entity.getSpecification());
        dto.setRemark(entity.getRemark());
        dto.setStatus(entity.getStatus());
        dto.setCreateBy(entity.getCreateBy());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateBy(entity.getUpdateBy());
        dto.setUpdateTime(entity.getUpdateTime());
        
        return dto;
    }
    
    /**
     * Entity List -> ResponseDTO List
     */
    public List<MaterialSpecResponseDTO> toResponseDTOList(List<MaterialSpec> entities) {
        if (entities == null) {
            return null;
        }
        
        return entities.stream()
                .map(this::toResponseDTO)
                .collect(Collectors.toList());
    }
}
