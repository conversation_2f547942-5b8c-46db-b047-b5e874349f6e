package com.redteamobile.procost.converter;

import com.redteamobile.procost.dto.material.MaterialCreateRequestDTO;
import com.redteamobile.procost.dto.material.MaterialResponseDTO;
import com.redteamobile.procost.dto.material.MaterialUpdateRequestDTO;
import com.redteamobile.procost.entity.Material;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Material DTO 转换器
 */
@Component
public class MaterialConverter {
    
    private final MaterialSpecConverter materialSpecConverter;
    
    public MaterialConverter(MaterialSpecConverter materialSpecConverter) {
        this.materialSpecConverter = materialSpecConverter;
    }
    
    /**
     * CreateRequestDTO -> Entity
     */
    public Material toEntity(MaterialCreateRequestDTO dto) {
        if (dto == null) {
            return null;
        }
        
        Material entity = new Material();
        entity.setEid(dto.getEid());
        entity.setMaterialSpecId(dto.getMaterialSpecId());
        entity.setRemark(dto.getRemark());
        entity.setStatus(dto.getStatus());
        entity.setCreateBy(dto.getCreateBy());
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        
        return entity;
    }
    
    /**
     * UpdateRequestDTO -> Entity
     */
    public Material toEntity(MaterialUpdateRequestDTO dto, Long id) {
        if (dto == null) {
            return null;
        }
        
        Material entity = new Material();
        entity.setId(id);
        entity.setEid(dto.getEid());
        entity.setMaterialSpecId(dto.getMaterialSpecId());
        entity.setRemark(dto.getRemark());
        entity.setStatus(dto.getStatus());
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setUpdateTime(LocalDateTime.now());
        
        return entity;
    }
    
    /**
     * Entity -> ResponseDTO
     */
    public MaterialResponseDTO toResponseDTO(Material entity) {
        if (entity == null) {
            return null;
        }
        
        MaterialResponseDTO dto = new MaterialResponseDTO();
        dto.setId(entity.getId());
        dto.setEid(entity.getEid());
        dto.setMaterialSpecId(entity.getMaterialSpecId());
        dto.setRemark(entity.getRemark());
        dto.setStatus(entity.getStatus());
        dto.setCreateBy(entity.getCreateBy());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateBy(entity.getUpdateBy());
        dto.setUpdateTime(entity.getUpdateTime());
        
        // 转换关联的物料规格信息
        if (entity.getMaterialSpec() != null) {
            dto.setMaterialSpec(materialSpecConverter.toResponseDTO(entity.getMaterialSpec()));
        }
        
        return dto;
    }
    
    /**
     * Entity List -> ResponseDTO List
     */
    public List<MaterialResponseDTO> toResponseDTOList(List<Material> entities) {
        if (entities == null) {
            return null;
        }
        
        return entities.stream()
                .map(this::toResponseDTO)
                .collect(Collectors.toList());
    }
}
