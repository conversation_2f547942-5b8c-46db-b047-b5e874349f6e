package com.redteamobile.procost.controller;

import com.redteamobile.procost.entity.InventoryTransaction;
import com.redteamobile.procost.service.InventoryTransactionService;
import com.redteamobile.procost.common.ApiResponse;
import com.redteamobile.procost.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 出入库记录管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/inventory-transactions")
@RequiredArgsConstructor
@Tag(name = "出入库记录管理", description = "出入库记录相关的增删改查操作")
public class InventoryTransactionController {
    
    private final InventoryTransactionService inventoryTransactionService;
    
    @Operation(summary = "根据ID查询出入库记录")
    @GetMapping("/{id}")
    public ApiResponse<InventoryTransaction> findById(@Parameter(description = "出入库记录ID") @PathVariable Long id) {
        InventoryTransaction transaction = inventoryTransactionService.findById(id);
        return ApiResponse.success(transaction);
    }
    
    @Operation(summary = "根据事务单号查询出入库记录")
    @GetMapping("/num/{transactionNum}")
    public ApiResponse<InventoryTransaction> findByTransactionNum(@Parameter(description = "事务单号") @PathVariable String transactionNum) {
        InventoryTransaction transaction = inventoryTransactionService.findByTransactionNum(transactionNum);
        return ApiResponse.success(transaction);
    }
    
    @Operation(summary = "根据物料ID查询出入库记录")
    @GetMapping("/material/{materialId}")
    public ApiResponse<List<InventoryTransaction>> findByMaterialId(@Parameter(description = "物料ID") @PathVariable Long materialId) {
        List<InventoryTransaction> transactions = inventoryTransactionService.findByMaterialId(materialId);
        return ApiResponse.success(transactions);
    }
    
    @Operation(summary = "根据物料规格ID查询出入库记录")
    @GetMapping("/spec/{materialSpecId}")
    public ApiResponse<List<InventoryTransaction>> findByMaterialSpecId(@Parameter(description = "物料规格ID") @PathVariable Long materialSpecId) {
        List<InventoryTransaction> transactions = inventoryTransactionService.findByMaterialSpecId(materialSpecId);
        return ApiResponse.success(transactions);
    }
    
    @Operation(summary = "分页查询出入库记录")
    @GetMapping("/page")
    public ApiResponse<PageResult<InventoryTransaction>> findByPage(
            @Parameter(description = "事务单号") @RequestParam(required = false) String transactionNum,
            @Parameter(description = "EID号") @RequestParam(required = false) String eid,
            @Parameter(description = "规格名称") @RequestParam(required = false) String specName,
            @Parameter(description = "事务类型：1-入库，2-出库") @RequestParam(required = false) Integer transactionType,
            @Parameter(description = "订单号") @RequestParam(required = false) String orderNum,
            @Parameter(description = "供应商ID") @RequestParam(required = false) Long supplierId,
            @Parameter(description = "操作员ID") @RequestParam(required = false) String operatorId,
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        
        PageResult<InventoryTransaction> result = inventoryTransactionService.findByPage(transactionNum, eid, specName,
                                                                                        transactionType, orderNum, supplierId,
                                                                                        operatorId, startDate, endDate,
                                                                                        status, page, size);
        return ApiResponse.success(result);
    }
    
    @Operation(summary = "创建出入库记录")
    @PostMapping
    public ApiResponse<InventoryTransaction> create(@Valid @RequestBody InventoryTransaction transaction) {
        InventoryTransaction createdTransaction = inventoryTransactionService.create(transaction);
        return ApiResponse.success(createdTransaction);
    }
    
    @Operation(summary = "更新出入库记录")
    @PutMapping("/{id}")
    public ApiResponse<InventoryTransaction> update(@Parameter(description = "出入库记录ID") @PathVariable Long id,
                                                   @Valid @RequestBody InventoryTransaction transaction) {
        transaction.setId(id);
        InventoryTransaction updatedTransaction = inventoryTransactionService.update(transaction);
        return ApiResponse.success(updatedTransaction);
    }
    
    @Operation(summary = "更新记录状态")
    @PutMapping("/{id}/status")
    public ApiResponse<Void> updateStatus(@Parameter(description = "出入库记录ID") @PathVariable Long id,
                                         @Parameter(description = "状态") @RequestParam Integer status) {
        inventoryTransactionService.updateStatus(id, status);
        return ApiResponse.success();
    }
    
    @Operation(summary = "删除出入库记录")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteById(@Parameter(description = "出入库记录ID") @PathVariable Long id) {
        inventoryTransactionService.deleteById(id);
        return ApiResponse.success();
    }
    
    @Operation(summary = "检查事务单号是否存在")
    @GetMapping("/exists/num/{transactionNum}")
    public ApiResponse<Boolean> existsByTransactionNum(@Parameter(description = "事务单号") @PathVariable String transactionNum) {
        boolean exists = inventoryTransactionService.existsByTransactionNum(transactionNum);
        return ApiResponse.success(exists);
    }
    
    @Operation(summary = "根据事务类型统计数量")
    @GetMapping("/count/type/{transactionType}")
    public ApiResponse<Integer> countByTransactionType(@Parameter(description = "事务类型") @PathVariable Integer transactionType) {
        int count = inventoryTransactionService.countByTransactionType(transactionType);
        return ApiResponse.success(count);
    }
    
    @Operation(summary = "统计指定时间范围内的出入库数量（按物料规格）")
    @GetMapping("/sum/spec/{materialSpecId}")
    public ApiResponse<Integer> sumQuantityByDateRangeForSpec(
            @Parameter(description = "物料规格ID") @PathVariable Long materialSpecId,
            @Parameter(description = "事务类型") @RequestParam Integer transactionType,
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        
        Integer sum = inventoryTransactionService.sumQuantityByDateRangeForSpec(materialSpecId, transactionType, startDate, endDate);
        return ApiResponse.success(sum);
    }
    
    @Operation(summary = "统计指定时间范围内的出入库数量（按具体物料）")
    @GetMapping("/sum/material/{materialId}")
    public ApiResponse<Integer> sumQuantityByDateRange(
            @Parameter(description = "物料ID") @PathVariable Long materialId,
            @Parameter(description = "事务类型") @RequestParam Integer transactionType,
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        
        Integer sum = inventoryTransactionService.sumQuantityByDateRange(materialId, transactionType, startDate, endDate);
        return ApiResponse.success(sum);
    }
    
    @Operation(summary = "处理入库业务")
    @PostMapping("/inbound")
    public ApiResponse<InventoryTransaction> processInbound(@Valid @RequestBody InventoryTransaction transaction) {
        InventoryTransaction result = inventoryTransactionService.processInbound(transaction);
        return ApiResponse.success(result);
    }
    
    @Operation(summary = "处理出库业务")
    @PostMapping("/outbound")
    public ApiResponse<InventoryTransaction> processOutbound(@Valid @RequestBody InventoryTransaction transaction) {
        InventoryTransaction result = inventoryTransactionService.processOutbound(transaction);
        return ApiResponse.success(result);
    }
    
    @Operation(summary = "批量导入出入库记录")
    @PostMapping("/batch-import")
    public ApiResponse<Void> batchImport(@RequestBody List<InventoryTransaction> transactions) {
        inventoryTransactionService.batchImport(transactions);
        return ApiResponse.success();
    }
    
    @Operation(summary = "导出出入库记录数据")
    @GetMapping("/export")
    public ApiResponse<List<InventoryTransaction>> exportData(
            @Parameter(description = "事务单号") @RequestParam(required = false) String transactionNum,
            @Parameter(description = "EID号") @RequestParam(required = false) String eid,
            @Parameter(description = "规格名称") @RequestParam(required = false) String specName,
            @Parameter(description = "事务类型") @RequestParam(required = false) Integer transactionType,
            @Parameter(description = "订单号") @RequestParam(required = false) String orderNum,
            @Parameter(description = "供应商ID") @RequestParam(required = false) Long supplierId,
            @Parameter(description = "操作员ID") @RequestParam(required = false) String operatorId,
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        List<InventoryTransaction> transactions = inventoryTransactionService.exportData(transactionNum, eid, specName,
                                                                                        transactionType, orderNum, supplierId,
                                                                                        operatorId, startDate, endDate, status);
        return ApiResponse.success(transactions);
    }
}
