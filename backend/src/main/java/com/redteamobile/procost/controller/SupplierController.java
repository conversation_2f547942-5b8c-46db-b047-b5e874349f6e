package com.redteamobile.procost.controller;

import com.redteamobile.procost.entity.Supplier;
import com.redteamobile.procost.service.SupplierService;
import com.redteamobile.procost.common.ApiResponse;
import com.redteamobile.procost.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/suppliers")
@RequiredArgsConstructor
@Tag(name = "供应商管理", description = "供应商相关的增删改查操作")
public class SupplierController {
    
    private final SupplierService supplierService;
    
    @Operation(summary = "根据ID查询供应商")
    @GetMapping("/{id}")
    public ApiResponse<Supplier> findById(@Parameter(description = "供应商ID") @PathVariable Long id) {
        Supplier supplier = supplierService.findById(id);
        return ApiResponse.success(supplier);
    }
    
    @Operation(summary = "根据供应商编码查询供应商")
    @GetMapping("/code/{supplierCode}")
    public ApiResponse<Supplier> findBySupplierCode(@Parameter(description = "供应商编码") @PathVariable String supplierCode) {
        Supplier supplier = supplierService.findBySupplierCode(supplierCode);
        return ApiResponse.success(supplier);
    }
    
    @Operation(summary = "查询所有供应商")
    @GetMapping("/all")
    public ApiResponse<List<Supplier>> findAll() {
        List<Supplier> suppliers = supplierService.findAll();
        return ApiResponse.success(suppliers);
    }
    
    @Operation(summary = "根据状态查询供应商")
    @GetMapping("/status/{status}")
    public ApiResponse<List<Supplier>> findByStatus(@Parameter(description = "状态：0-停用，1-启用") @PathVariable Integer status) {
        List<Supplier> suppliers = supplierService.findByStatus(status);
        return ApiResponse.success(suppliers);
    }
    
    @Operation(summary = "分页查询供应商")
    @GetMapping("/page")
    public ApiResponse<PageResult<Supplier>> findByPage(
            @Parameter(description = "供应商编码") @RequestParam(required = false) String supplierCode,
            @Parameter(description = "供应商名称") @RequestParam(required = false) String supplierName,
            @Parameter(description = "地址") @RequestParam(required = false) String address,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        
        PageResult<Supplier> result = supplierService.findByPage(supplierCode, supplierName, 
                                                               address, status, page, size);
        return ApiResponse.success(result);
    }
    
    @Operation(summary = "创建供应商")
    @PostMapping
    public ApiResponse<Supplier> create(@Valid @RequestBody Supplier supplier) {
        Supplier createdSupplier = supplierService.create(supplier);
        return ApiResponse.success(createdSupplier);
    }
    
    @Operation(summary = "更新供应商")
    @PutMapping("/{id}")
    public ApiResponse<Supplier> update(@Parameter(description = "供应商ID") @PathVariable Long id,
                                       @Valid @RequestBody Supplier supplier) {
        supplier.setId(id);
        Supplier updatedSupplier = supplierService.update(supplier);
        return ApiResponse.success(updatedSupplier);
    }
    
    @Operation(summary = "删除供应商")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteById(@Parameter(description = "供应商ID") @PathVariable Long id) {
        supplierService.deleteById(id);
        return ApiResponse.success();
    }
    
    @Operation(summary = "检查供应商编码是否存在")
    @GetMapping("/exists/code/{supplierCode}")
    public ApiResponse<Boolean> existsBySupplierCode(@Parameter(description = "供应商编码") @PathVariable String supplierCode) {
        boolean exists = supplierService.existsBySupplierCode(supplierCode);
        return ApiResponse.success(exists);
    }
    
    @Operation(summary = "根据状态统计供应商数量")
    @GetMapping("/count/status/{status}")
    public ApiResponse<Integer> countByStatus(@Parameter(description = "状态") @PathVariable Integer status) {
        int count = supplierService.countByStatus(status);
        return ApiResponse.success(count);
    }
    
    @Operation(summary = "批量导入供应商")
    @PostMapping("/batch-import")
    public ApiResponse<Void> batchImport(@RequestBody List<Supplier> suppliers) {
        supplierService.batchImport(suppliers);
        return ApiResponse.success();
    }
    
    @Operation(summary = "导出供应商数据")
    @GetMapping("/export")
    public ApiResponse<List<Supplier>> exportData(
            @Parameter(description = "供应商编码") @RequestParam(required = false) String supplierCode,
            @Parameter(description = "供应商名称") @RequestParam(required = false) String supplierName,
            @Parameter(description = "地址") @RequestParam(required = false) String address,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        List<Supplier> suppliers = supplierService.exportData(supplierCode, supplierName, address, status);
        return ApiResponse.success(suppliers);
    }
}
