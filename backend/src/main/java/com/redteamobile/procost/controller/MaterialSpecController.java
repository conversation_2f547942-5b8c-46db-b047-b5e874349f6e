package com.redteamobile.procost.controller;

import com.redteamobile.procost.entity.MaterialSpec;
import com.redteamobile.procost.service.MaterialSpecService;
import com.redteamobile.procost.common.ApiResponse;
import com.redteamobile.procost.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 物料规格管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/material-specs")
@RequiredArgsConstructor
@Tag(name = "物料规格管理", description = "物料规格相关的增删改查操作")
public class MaterialSpecController {
    
    private final MaterialSpecService materialSpecService;
    
    @Operation(summary = "根据ID查询物料规格")
    @GetMapping("/{id}")
    public ApiResponse<MaterialSpec> findById(@Parameter(description = "物料规格ID") @PathVariable Long id) {
        MaterialSpec materialSpec = materialSpecService.findById(id);
        return ApiResponse.success(materialSpec);
    }
    
    @Operation(summary = "根据规格编码查询物料规格")
    @GetMapping("/code/{specCode}")
    public ApiResponse<MaterialSpec> findBySpecCode(@Parameter(description = "规格编码") @PathVariable String specCode) {
        MaterialSpec materialSpec = materialSpecService.findBySpecCode(specCode);
        return ApiResponse.success(materialSpec);
    }
    
    @Operation(summary = "查询所有物料规格")
    @GetMapping("/all")
    public ApiResponse<List<MaterialSpec>> findAll() {
        List<MaterialSpec> materialSpecs = materialSpecService.findAll();
        return ApiResponse.success(materialSpecs);
    }
    
    @Operation(summary = "根据状态查询物料规格")
    @GetMapping("/status/{status}")
    public ApiResponse<List<MaterialSpec>> findByStatus(@Parameter(description = "状态：0-停用，1-启用") @PathVariable Integer status) {
        List<MaterialSpec> materialSpecs = materialSpecService.findByStatus(status);
        return ApiResponse.success(materialSpecs);
    }
    
    @Operation(summary = "分页查询物料规格")
    @GetMapping("/page")
    public ApiResponse<PageResult<MaterialSpec>> findByPage(
            @Parameter(description = "规格编码") @RequestParam(required = false) String specCode,
            @Parameter(description = "规格名称") @RequestParam(required = false) String specName,
            @Parameter(description = "类别") @RequestParam(required = false) String category,
            @Parameter(description = "卡片类型") @RequestParam(required = false) String cardType,
            @Parameter(description = "环境") @RequestParam(required = false) String environment,
            @Parameter(description = "制造商") @RequestParam(required = false) String manufacturer,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        
        PageResult<MaterialSpec> result = materialSpecService.findByPage(specCode, specName, category,
                                                                        cardType, environment, manufacturer,
                                                                        status, page, size);
        return ApiResponse.success(result);
    }
    
    @Operation(summary = "根据类别查询物料规格")
    @GetMapping("/category/{category}")
    public ApiResponse<List<MaterialSpec>> findByCategory(@Parameter(description = "类别") @PathVariable String category) {
        List<MaterialSpec> materialSpecs = materialSpecService.findByCategory(category);
        return ApiResponse.success(materialSpecs);
    }
    
    @Operation(summary = "根据制造商查询物料规格")
    @GetMapping("/manufacturer/{manufacturer}")
    public ApiResponse<List<MaterialSpec>> findByManufacturer(@Parameter(description = "制造商") @PathVariable String manufacturer) {
        List<MaterialSpec> materialSpecs = materialSpecService.findByManufacturer(manufacturer);
        return ApiResponse.success(materialSpecs);
    }
    
    @Operation(summary = "创建物料规格")
    @PostMapping
    public ApiResponse<MaterialSpec> create(@Valid @RequestBody MaterialSpec materialSpec) {
        MaterialSpec createdSpec = materialSpecService.create(materialSpec);
        return ApiResponse.success(createdSpec);
    }
    
    @Operation(summary = "更新物料规格")
    @PutMapping("/{id}")
    public ApiResponse<MaterialSpec> update(@Parameter(description = "物料规格ID") @PathVariable Long id,
                                           @Valid @RequestBody MaterialSpec materialSpec) {
        materialSpec.setId(id);
        MaterialSpec updatedSpec = materialSpecService.update(materialSpec);
        return ApiResponse.success(updatedSpec);
    }
    
    @Operation(summary = "删除物料规格")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteById(@Parameter(description = "物料规格ID") @PathVariable Long id) {
        materialSpecService.deleteById(id);
        return ApiResponse.success();
    }
    
    @Operation(summary = "检查规格编码是否存在")
    @GetMapping("/exists/code/{specCode}")
    public ApiResponse<Boolean> existsBySpecCode(@Parameter(description = "规格编码") @PathVariable String specCode) {
        boolean exists = materialSpecService.existsBySpecCode(specCode);
        return ApiResponse.success(exists);
    }
    
    @Operation(summary = "根据状态统计物料规格数量")
    @GetMapping("/count/status/{status}")
    public ApiResponse<Integer> countByStatus(@Parameter(description = "状态") @PathVariable Integer status) {
        int count = materialSpecService.countByStatus(status);
        return ApiResponse.success(count);
    }
    
    @Operation(summary = "根据类别统计物料规格数量")
    @GetMapping("/count/category/{category}")
    public ApiResponse<Integer> countByCategory(@Parameter(description = "类别") @PathVariable String category) {
        int count = materialSpecService.countByCategory(category);
        return ApiResponse.success(count);
    }
    
    @Operation(summary = "批量导入物料规格")
    @PostMapping("/batch-import")
    public ApiResponse<Void> batchImport(@RequestBody List<MaterialSpec> materialSpecs) {
        materialSpecService.batchImport(materialSpecs);
        return ApiResponse.success();
    }
    
    @Operation(summary = "导出物料规格数据")
    @GetMapping("/export")
    public ApiResponse<List<MaterialSpec>> exportData(
            @Parameter(description = "规格编码") @RequestParam(required = false) String specCode,
            @Parameter(description = "规格名称") @RequestParam(required = false) String specName,
            @Parameter(description = "类别") @RequestParam(required = false) String category,
            @Parameter(description = "卡片类型") @RequestParam(required = false) String cardType,
            @Parameter(description = "环境") @RequestParam(required = false) String environment,
            @Parameter(description = "制造商") @RequestParam(required = false) String manufacturer,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        List<MaterialSpec> materialSpecs = materialSpecService.exportData(specCode, specName, category,
                                                                         cardType, environment, manufacturer, status);
        return ApiResponse.success(materialSpecs);
    }
}
