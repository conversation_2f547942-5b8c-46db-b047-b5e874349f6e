package com.redteamobile.procost.controller;

import com.redteamobile.procost.converter.MaterialConverter;
import com.redteamobile.procost.dto.material.MaterialCreateRequestDTO;
import com.redteamobile.procost.dto.material.MaterialQueryDTO;
import com.redteamobile.procost.dto.material.MaterialResponseDTO;
import com.redteamobile.procost.dto.material.MaterialUpdateRequestDTO;
import com.redteamobile.procost.entity.Material;
import com.redteamobile.procost.service.MaterialService;
import com.redteamobile.procost.common.ApiResponse;
import com.redteamobile.procost.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 物料管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/materials")
@RequiredArgsConstructor
@Tag(name = "物料管理", description = "物料相关的增删改查操作")
public class MaterialController {
    
    private final MaterialService materialService;
    private final MaterialConverter materialConverter;
    
    @Operation(summary = "根据ID查询物料")
    @GetMapping("/{id}")
    public ApiResponse<MaterialResponseDTO> findById(@Parameter(description = "物料ID") @PathVariable Long id) {
        Material material = materialService.findById(id);
        MaterialResponseDTO responseDTO = materialConverter.toResponseDTO(material);
        return ApiResponse.success(responseDTO);
    }
    
    @Operation(summary = "根据EID查询物料")
    @GetMapping("/eid/{eid}")
    public ApiResponse<Material> findByEid(@Parameter(description = "EID号") @PathVariable String eid) {
        Material material = materialService.findByEid(eid);
        return ApiResponse.success(material);
    }
    
    @Operation(summary = "查询所有物料")
    @GetMapping("/all")
    public ApiResponse<List<Material>> findAll() {
        List<Material> materials = materialService.findAll();
        return ApiResponse.success(materials);
    }
    
    @Operation(summary = "根据状态查询物料")
    @GetMapping("/status/{status}")
    public ApiResponse<List<Material>> findByStatus(@Parameter(description = "状态：0-停用，1-启用") @PathVariable Integer status) {
        List<Material> materials = materialService.findByStatus(status);
        return ApiResponse.success(materials);
    }
    
    @Operation(summary = "分页查询物料")
    @GetMapping("/page")
    public ApiResponse<PageResult<MaterialResponseDTO>> findByPage(MaterialQueryDTO queryDTO) {
        PageResult<Material> result = materialService.findByPage(
            queryDTO.getEid(), queryDTO.getMaterialSpecId(), queryDTO.getCategory(),
            queryDTO.getCardType(), queryDTO.getEnvironment(), queryDTO.getManufacturer(),
            queryDTO.getStatus(), queryDTO.getPage(), queryDTO.getSize()
        );
        
        // 转换结果
        List<MaterialResponseDTO> responseDTOs = materialConverter.toResponseDTOList(result.getRecords());
        PageResult<MaterialResponseDTO> responseResult = new PageResult<>(
            responseDTOs, result.getTotal(), result.getCurrent(), result.getSize()
        );
        
        return ApiResponse.success(responseResult);
    }
    
    @Operation(summary = "根据规格ID查询物料列表")
    @GetMapping("/spec/{materialSpecId}")
    public ApiResponse<List<Material>> findByMaterialSpecId(@Parameter(description = "物料规格ID") @PathVariable Long materialSpecId) {
        List<Material> materials = materialService.findByMaterialSpecId(materialSpecId);
        return ApiResponse.success(materials);
    }
    
    @Operation(summary = "创建物料")
    @PostMapping
    public ApiResponse<MaterialResponseDTO> create(@Valid @RequestBody MaterialCreateRequestDTO requestDTO) {
        Material material = materialConverter.toEntity(requestDTO);
        Material createdMaterial = materialService.create(material);
        MaterialResponseDTO responseDTO = materialConverter.toResponseDTO(createdMaterial);
        return ApiResponse.success(responseDTO);
    }
    
    @Operation(summary = "更新物料")
    @PutMapping("/{id}")
    public ApiResponse<MaterialResponseDTO> update(@Parameter(description = "物料ID") @PathVariable Long id, 
                                                  @Valid @RequestBody MaterialUpdateRequestDTO requestDTO) {
        Material material = materialConverter.toEntity(requestDTO, id);
        Material updatedMaterial = materialService.update(material);
        MaterialResponseDTO responseDTO = materialConverter.toResponseDTO(updatedMaterial);
        return ApiResponse.success(responseDTO);
    }
    
    @Operation(summary = "删除物料")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteById(@Parameter(description = "物料ID") @PathVariable Long id) {
        materialService.deleteById(id);
        return ApiResponse.<Void>success();
    }
    
    @Operation(summary = "检查EID是否存在")
    @GetMapping("/exists/eid/{eid}")
    public ApiResponse<Boolean> existsByEid(@Parameter(description = "EID号") @PathVariable String eid) {
        boolean exists = materialService.existsByEid(eid);
        return ApiResponse.success(exists);
    }
    
    @Operation(summary = "根据状态统计物料数量")
    @GetMapping("/count/status/{status}")
    public ApiResponse<Integer> countByStatus(@Parameter(description = "状态") @PathVariable Integer status) {
        int count = materialService.countByStatus(status);
        return ApiResponse.success(count);
    }
    
    @Operation(summary = "批量导入物料")
    @PostMapping("/batch-import")
    public ApiResponse<Void> batchImport(@RequestBody List<Material> materials) {
        materialService.batchImport(materials);
        return ApiResponse.success();
    }
    
    @Operation(summary = "导出物料数据")
    @GetMapping("/export")
    public ApiResponse<List<Material>> exportData(
            @Parameter(description = "EID号") @RequestParam(required = false) String eid,
            @Parameter(description = "物料规格ID") @RequestParam(required = false) Long materialSpecId,
            @Parameter(description = "类别") @RequestParam(required = false) String category,
            @Parameter(description = "卡片类型") @RequestParam(required = false) String cardType,
            @Parameter(description = "环境") @RequestParam(required = false) String environment,
            @Parameter(description = "制造商") @RequestParam(required = false) String manufacturer,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        List<Material> materials = materialService.exportData(eid, materialSpecId, category, 
                                                            cardType, environment, manufacturer, status);
        return ApiResponse.success(materials);
    }
}
