package com.redteamobile.procost.controller;

import com.redteamobile.procost.entity.Inventory;
import com.redteamobile.procost.service.InventoryService;
import com.redteamobile.procost.common.ApiResponse;
import com.redteamobile.procost.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/inventories")
@RequiredArgsConstructor
@Tag(name = "库存管理", description = "库存相关的增删改查操作")
public class InventoryController {
    
    private final InventoryService inventoryService;
    
    @Operation(summary = "根据ID查询库存")
    @GetMapping("/{id}")
    public ApiResponse<Inventory> findById(@Parameter(description = "库存ID") @PathVariable Long id) {
        Inventory inventory = inventoryService.findById(id);
        return ApiResponse.success(inventory);
    }
    
    @Operation(summary = "根据物料规格ID查询库存")
    @GetMapping("/spec/{materialSpecId}")
    public ApiResponse<Inventory> findByMaterialSpecId(@Parameter(description = "物料规格ID") @PathVariable Long materialSpecId) {
        Inventory inventory = inventoryService.findByMaterialSpecId(materialSpecId);
        return ApiResponse.success(inventory);
    }
    
    @Operation(summary = "查询所有库存")
    @GetMapping("/all")
    public ApiResponse<List<Inventory>> findAll() {
        List<Inventory> inventories = inventoryService.findAll();
        return ApiResponse.success(inventories);
    }
    
    @Operation(summary = "分页查询库存")
    @GetMapping("/page")
    public ApiResponse<PageResult<Inventory>> findByPage(
            @Parameter(description = "物料规格ID") @RequestParam(required = false) Long materialSpecId,
            @Parameter(description = "类别") @RequestParam(required = false) String category,
            @Parameter(description = "卡片类型") @RequestParam(required = false) String cardType,
            @Parameter(description = "环境") @RequestParam(required = false) String environment,
            @Parameter(description = "制造商") @RequestParam(required = false) String manufacturer,
            @Parameter(description = "最小库存") @RequestParam(required = false) Integer minStock,
            @Parameter(description = "最大库存") @RequestParam(required = false) Integer maxStock,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        
        PageResult<Inventory> result = inventoryService.findByPage(materialSpecId, category, cardType,
                                                                 environment, manufacturer, minStock,
                                                                 maxStock, page, size);
        return ApiResponse.success(result);
    }
    
    @Operation(summary = "查询库存预警列表")
    @GetMapping("/low-stock")
    public ApiResponse<List<Inventory>> findLowStockInventories() {
        List<Inventory> inventories = inventoryService.findLowStockInventories();
        return ApiResponse.success(inventories);
    }
    
    @Operation(summary = "创建库存记录")
    @PostMapping
    public ApiResponse<Inventory> create(@Valid @RequestBody Inventory inventory) {
        Inventory createdInventory = inventoryService.create(inventory);
        return ApiResponse.success(createdInventory);
    }
    
    @Operation(summary = "更新库存信息")
    @PutMapping("/{id}")
    public ApiResponse<Inventory> update(@Parameter(description = "库存ID") @PathVariable Long id,
                                        @Valid @RequestBody Inventory inventory) {
        inventory.setId(id);
        Inventory updatedInventory = inventoryService.update(inventory);
        return ApiResponse.success(updatedInventory);
    }
    
    @Operation(summary = "更新库存数量")
    @PutMapping("/stock/{materialSpecId}")
    public ApiResponse<Void> updateStock(@Parameter(description = "物料规格ID") @PathVariable Long materialSpecId,
                                        @Parameter(description = "当前库存") @RequestParam Integer currentStock,
                                        @Parameter(description = "可用库存") @RequestParam Integer availableStock,
                                        @Parameter(description = "预留库存") @RequestParam Integer reservedStock) {
        inventoryService.updateStock(materialSpecId, currentStock, availableStock, reservedStock);
        return ApiResponse.success();
    }
    
    @Operation(summary = "入库操作")
    @PostMapping("/increase/{materialSpecId}")
    public ApiResponse<Void> increaseStock(@Parameter(description = "物料规格ID") @PathVariable Long materialSpecId,
                                          @Parameter(description = "入库数量") @RequestParam Integer quantity) {
        inventoryService.increaseStock(materialSpecId, quantity);
        return ApiResponse.success();
    }
    
    @Operation(summary = "出库操作")
    @PostMapping("/decrease/{materialSpecId}")
    public ApiResponse<Void> decreaseStock(@Parameter(description = "物料规格ID") @PathVariable Long materialSpecId,
                                          @Parameter(description = "出库数量") @RequestParam Integer quantity) {
        inventoryService.decreaseStock(materialSpecId, quantity);
        return ApiResponse.success();
    }
    
    @Operation(summary = "删除库存记录")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteById(@Parameter(description = "库存ID") @PathVariable Long id) {
        inventoryService.deleteById(id);
        return ApiResponse.success();
    }
    
    @Operation(summary = "根据物料规格ID删除库存")
    @DeleteMapping("/spec/{materialSpecId}")
    public ApiResponse<Void> deleteByMaterialSpecId(@Parameter(description = "物料规格ID") @PathVariable Long materialSpecId) {
        inventoryService.deleteByMaterialSpecId(materialSpecId);
        return ApiResponse.success();
    }
    
    @Operation(summary = "统计库存总数")
    @GetMapping("/count/all")
    public ApiResponse<Long> countAll() {
        Long count = inventoryService.countAll();
        return ApiResponse.success(count);
    }
    
    @Operation(summary = "统计预警库存数量")
    @GetMapping("/count/low-stock")
    public ApiResponse<Long> countLowStock() {
        Long count = inventoryService.countLowStock();
        return ApiResponse.success(count);
    }
    
    @Operation(summary = "导出库存数据")
    @GetMapping("/export")
    public ApiResponse<List<Inventory>> exportData(
            @Parameter(description = "物料规格ID") @RequestParam(required = false) Long materialSpecId,
            @Parameter(description = "类别") @RequestParam(required = false) String category,
            @Parameter(description = "卡片类型") @RequestParam(required = false) String cardType,
            @Parameter(description = "环境") @RequestParam(required = false) String environment,
            @Parameter(description = "制造商") @RequestParam(required = false) String manufacturer,
            @Parameter(description = "最小库存") @RequestParam(required = false) Integer minStock,
            @Parameter(description = "最大库存") @RequestParam(required = false) Integer maxStock) {
        
        List<Inventory> inventories = inventoryService.exportData(materialSpecId, category, cardType,
                                                                environment, manufacturer, minStock, maxStock);
        return ApiResponse.success(inventories);
    }
}
