package com.redteamobile.procost.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;

/**
 * 供应商实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class Supplier extends BaseEntity {
    
    /**
     * 供应商编码
     */
    @NotBlank(message = "供应商编码不能为空")
    @Size(max = 50, message = "供应商编码长度不能超过50个字符")
    private String supplierCode;
    
    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空")
    @Size(max = 100, message = "供应商名称长度不能超过100个字符")
    private String supplierName;

    
    /**
     * 联系邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String contactEmail;
    
    /**
     * 地址
     */
    @Size(max = 200, message = "地址长度不能超过200个字符")
    private String address;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status = 1; // 默认启用
}
