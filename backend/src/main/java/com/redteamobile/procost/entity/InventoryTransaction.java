package com.redteamobile.procost.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 出入库记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class InventoryTransaction extends BaseEntity {
    
    /**
     * 事务单号
     */
    @NotBlank(message = "事务单号不能为空")
    private String transactionNum;
    
    /**
     * 物料ID（具体卡片）
     */
    private Long materialId;
    
    /**
     * 物料规格ID
     */
    @NotNull(message = "物料规格ID不能为空")
    private Long materialSpecId;
    
    /**
     * 事务类型：1-入库，2-出库，3-调拨，4-盘点
     */
    @NotNull(message = "事务类型不能为空")
    private Integer transactionType;
    
    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于0")
    private Integer quantity;
    
    /**
     * 单价
     */
    @DecimalMin(value = "0.0", message = "单价不能为负数")
    private BigDecimal unitPrice;
    
    /**
     * 总金额
     */
    @DecimalMin(value = "0.0", message = "总金额不能为负数")
    private BigDecimal totalAmount;
    
    /**
     * 订单号
     */
    private String orderNum;
    
    /**
     * 供应商ID
     */
    private Long supplierId;
    
    /**
     * 批次号
     */
    private String batchNum;
    
    /**
     * 操作人ID（Keycloak用户ID）
     */
    private String operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;
    
    /**
     * 事务日期
     */
    @NotNull(message = "事务日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime transactionDate;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 状态：0-已取消，1-已完成
     */
    private Integer status;
    
    // 关联的物料信息（用于查询时返回）
    private Material material;
    
    // 关联的物料规格信息（用于查询时返回）
    private MaterialSpec materialSpec;
    
    // 关联的供应商信息（用于查询时返回）
    private Supplier supplier;
    
    {
        this.status = 1; // 默认已完成
        this.transactionDate = LocalDateTime.now();
    }
    
    // 事务类型常量
    public static final int TYPE_IN = 1;      // 入库
    public static final int TYPE_OUT = 2;     // 出库
    public static final int TYPE_TRANSFER = 3; // 调拨
    public static final int TYPE_CHECK = 4;   // 盘点
    
    // 状态常量
    public static final int STATUS_CANCELLED = 0; // 已取消
    public static final int STATUS_COMPLETED = 1; // 已完成

    
    /**
     * 获取事务类型描述
     */
    public String getTransactionTypeDesc() {
        if (transactionType == null) return "";
        switch (transactionType) {
            case TYPE_IN: return "入库";
            case TYPE_OUT: return "出库";
            case TYPE_TRANSFER: return "调拨";
            case TYPE_CHECK: return "盘点";
            default: return "未知";
        }
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case STATUS_CANCELLED: return "已取消";
            case STATUS_COMPLETED: return "已完成";
            default: return "未知";
        }
    }
}
