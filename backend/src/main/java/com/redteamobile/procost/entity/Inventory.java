package com.redteamobile.procost.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;

/**
 * 库存实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class Inventory extends BaseEntity {
    
    /**
     * 物料规格ID
     */
    private Long materialSpecId;
    
    /**
     * 当前库存数量
     */
    @Min(value = 0, message = "库存数量不能为负数")
    private Integer currentStock;
    
    /**
     * 可用库存数量
     */
    @Min(value = 0, message = "可用库存数量不能为负数")
    private Integer availableStock;
    
    /**
     * 预留库存数量
     */
    @Min(value = 0, message = "预留库存数量不能为负数")
    private Integer reservedStock;
    
    /**
     * 最小库存预警
     */
    @Min(value = 0, message = "最小库存预警不能为负数")
    private Integer minStock;
    
    /**
     * 最大库存上限
     */
    @Min(value = 0, message = "最大库存上限不能为负数")
    private Integer maxStock;
    
    /**
     * 最后入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastInTime;
    
    /**
     * 最后出库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastOutTime;
    
    // 关联的物料规格信息（用于查询时返回）
    private MaterialSpec materialSpec;
    
    {
        this.currentStock = 0;
        this.availableStock = 0;
        this.reservedStock = 0;
        this.minStock = 0;
        this.maxStock = 0;
    }

    
    /**
     * 检查是否库存不足
     */
    public boolean isLowStock() {
        return currentStock != null && minStock != null && currentStock <= minStock;
    }
    
    /**
     * 检查是否库存过多
     */
    public boolean isOverStock() {
        return currentStock != null && maxStock != null && maxStock > 0 && currentStock >= maxStock;
    }
}
