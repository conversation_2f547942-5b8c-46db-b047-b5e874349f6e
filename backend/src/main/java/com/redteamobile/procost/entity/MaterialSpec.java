package com.redteamobile.procost.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 物料规格实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MaterialSpec extends BaseEntity {
    
    /**
     * 规格编码
     */
    @NotBlank(message = "规格编码不能为空")
    @Size(max = 50, message = "规格编码长度不能超过50个字符")
    private String specCode;
    
    /**
     * 规格名称
     */
    @NotBlank(message = "规格名称不能为空")
    @Size(max = 100, message = "规格名称长度不能超过100个字符")
    private String specName;
    
    /**
     * 物料类别
     */
    @Size(max = 50, message = "物料类别长度不能超过50个字符")
    private String category;
    
    /**
     * 卡片类型
     */
    @Size(max = 50, message = "卡片类型长度不能超过50个字符")
    private String cardType;
    
    /**
     * 卡片等级
     */
    @Size(max = 50, message = "卡片等级长度不能超过50个字符")
    private String cardLevel;
    
    /**
     * 环境（Prod/Staging等）
     */
    @Size(max = 50, message = "环境长度不能超过50个字符")
    private String environment;
    
    /**
     * 制卡商
     */
    @Size(max = 50, message = "制卡商长度不能超过50个字符")
    private String manufacturer;
    
    /**
     * 相关配置信息（JSON格式）
     */
    private String configuration;
    
    /**
     * 单位
     */
    @Size(max = 20, message = "单位长度不能超过20个字符")
    private String unit;
    
    /**
     * 规格说明
     */
    @Size(max = 200, message = "规格说明长度不能超过200个字符")
    private String specification;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 状态：0-停用，1-启用
     */
    private Integer status = 1; // 默认启用
    
    /**
     * 创建人（Keycloak用户ID）
     */
    private String createBy;
    
    /**
     * 更新人（Keycloak用户ID）
     */
    private String updateBy;
    
    {
        this.unit = "张"; // 默认单位
    }
}
