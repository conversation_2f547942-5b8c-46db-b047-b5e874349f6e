package com.redteamobile.procost.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 物料实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class Material extends BaseEntity {
    
        /**
     * EID号
     */
    @NotBlank(message = "EID号不能为空")
    @Size(max = 50, message = "EID号长度不能超过50个字符")
    private String eid;
    
    /**
     * 物料规格ID
     */
    private Long materialSpecId;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 状态：0-停用，1-启用
     */
    private Integer status = 1; // 默认启用
    
    /**
     * 创建人（Keycloak用户ID）
     */
    private String createBy;
    
    /**
     * 更新人（Keycloak用户ID）
     */
    private String updateBy;
    
    // 关联的物料规格信息（用于查询时返回）
    private MaterialSpec materialSpec;
}

