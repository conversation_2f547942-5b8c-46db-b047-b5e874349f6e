<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redteamobile.procost.mapper.SupplierMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.redteamobile.procost.entity.Supplier">
        <id column="id" property="id"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="address" property="address"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, supplier_code, supplier_name, address, phone, email, remark, status, 
        create_by, create_time, update_by, update_time
    </sql>

    <!-- 根据供应商编码查询供应商 -->
    <select id="findBySupplierCode" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM supplier
        WHERE supplier_code = #{supplierCode}
    </select>

    <!-- 根据状态查询供应商 -->
    <select id="findByStatus" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM supplier
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- 多条件分页查询供应商 -->
    <select id="findByPage" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM supplier
        <where>
            <if test="supplierCode != null and supplierCode != ''">
                AND supplier_code LIKE CONCAT('%', #{supplierCode}, '%')
            </if>
            <if test="supplierName != null and supplierName != ''">
                AND supplier_name LIKE CONCAT('%', #{supplierName}, '%')
            </if>
            <if test="address != null and address != ''">
                AND address LIKE CONCAT('%', #{address}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 检查供应商编码是否存在 -->
    <select id="existsBySupplierCode" resultType="int">
        SELECT COUNT(*)
        FROM supplier
        WHERE supplier_code = #{supplierCode}
    </select>

    <!-- 检查供应商编码是否存在（排除指定ID） -->
    <select id="existsBySupplierCodeExcludeId" resultType="int">
        SELECT COUNT(*)
        FROM supplier
        WHERE supplier_code = #{supplierCode} AND id != #{excludeId}
    </select>

    <!-- 根据状态统计供应商数量 -->
    <select id="countByStatus" resultType="int">
        SELECT COUNT(*)
        FROM supplier
        WHERE status = #{status}
    </select>

</mapper>
