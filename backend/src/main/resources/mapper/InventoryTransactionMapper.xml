<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redteamobile.procost.mapper.InventoryTransactionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.redteamobile.procost.entity.InventoryTransaction">
        <id column="id" property="id"/>
        <result column="transaction_num" property="transactionNum"/>
        <result column="material_id" property="materialId"/>
        <result column="material_spec_id" property="materialSpecId"/>
        <result column="transaction_type" property="transactionType"/>
        <result column="quantity" property="quantity"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="order_num" property="orderNum"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="batch_num" property="batchNum"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="transaction_date" property="transactionDate"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 带关联信息的结果映射 -->
    <resultMap id="TransactionWithAssociationResultMap" type="com.redteamobile.procost.entity.InventoryTransaction" extends="BaseResultMap">
        <association property="material" javaType="com.redteamobile.procost.entity.Material">
            <id column="material_id" property="id"/>
            <result column="eid" property="eid"/>
        </association>
        <association property="materialSpec" javaType="com.redteamobile.procost.entity.MaterialSpec">
            <id column="spec_id" property="id"/>
            <result column="spec_code" property="specCode"/>
            <result column="spec_name" property="specName"/>
            <result column="category" property="category"/>
            <result column="card_type" property="cardType"/>
            <result column="card_level" property="cardLevel"/>
            <result column="environment" property="environment"/>
            <result column="manufacturer" property="manufacturer"/>
            <result column="configuration" property="configuration"/>
            <result column="unit" property="unit"/>
            <result column="specification" property="specification"/>
        </association>
        <association property="supplier" javaType="com.redteamobile.procost.entity.Supplier">
            <id column="supplier_id" property="id"/>
            <result column="supplier_name" property="supplierName"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        t.id, t.transaction_num, t.material_id, t.material_spec_id, t.transaction_type, 
        t.quantity, t.unit_price, t.total_amount, t.order_num, t.supplier_id, t.batch_num, 
        t.operator_id, t.operator_name, t.transaction_date, t.remark, t.status, 
        t.create_time, t.update_time
    </sql>

    <!-- 带关联信息的查询字段 -->
    <sql id="Transaction_With_Association_Column_List">
        t.id, t.transaction_num, t.material_id, t.material_spec_id, t.transaction_type, 
        t.quantity, t.unit_price, t.total_amount, t.order_num, t.supplier_id, t.batch_num, 
        t.operator_id, t.operator_name, t.transaction_date, t.remark, t.status, 
        t.create_time, t.update_time,
        m.eid,
        ms.id as spec_id, ms.spec_code, ms.spec_name, ms.category, ms.card_type, 
        ms.card_level, ms.environment, ms.manufacturer, ms.configuration, 
        ms.unit, ms.specification,
        s.supplier_name
    </sql>

    <!-- 根据事务单号查询出入库记录 -->
    <select id="findByTransactionNum" resultMap="TransactionWithAssociationResultMap">
        SELECT 
        <include refid="Transaction_With_Association_Column_List"/>
        FROM inventory_transaction t
        LEFT JOIN material m ON t.material_id = m.id
        LEFT JOIN material_spec ms ON t.material_spec_id = ms.id
        LEFT JOIN supplier s ON t.supplier_id = s.id
        WHERE t.transaction_num = #{transactionNum}
    </select>

    <!-- 根据物料ID查询出入库记录 -->
    <select id="findByMaterialId" resultMap="TransactionWithAssociationResultMap">
        SELECT 
        <include refid="Transaction_With_Association_Column_List"/>
        FROM inventory_transaction t
        LEFT JOIN material m ON t.material_id = m.id
        LEFT JOIN material_spec ms ON t.material_spec_id = ms.id
        LEFT JOIN supplier s ON t.supplier_id = s.id
        WHERE t.material_id = #{materialId}
        ORDER BY t.transaction_date DESC
    </select>

    <!-- 多条件分页查询出入库记录 -->
    <select id="findByPage" resultMap="TransactionWithAssociationResultMap">
        SELECT 
        <include refid="Transaction_With_Association_Column_List"/>
        FROM inventory_transaction t
        LEFT JOIN material m ON t.material_id = m.id
        LEFT JOIN material_spec ms ON t.material_spec_id = ms.id
        LEFT JOIN supplier s ON t.supplier_id = s.id
        <where>
            <if test="transactionNum != null and transactionNum != ''">
                AND t.transaction_num LIKE CONCAT('%', #{transactionNum}, '%')
            </if>
            <if test="eid != null and eid != ''">
                AND m.eid LIKE CONCAT('%', #{eid}, '%')
            </if>
            <if test="specName != null and specName != ''">
                AND ms.spec_name LIKE CONCAT('%', #{specName}, '%')
            </if>
            <if test="transactionType != null">
                AND t.transaction_type = #{transactionType}
            </if>
            <if test="orderNum != null and orderNum != ''">
                AND t.order_num LIKE CONCAT('%', #{orderNum}, '%')
            </if>
            <if test="supplierId != null">
                AND t.supplier_id = #{supplierId}
            </if>
            <if test="operatorId != null and operatorId != ''">
                AND t.operator_id = #{operatorId}
            </if>
            <if test="startDate != null">
                AND t.transaction_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.transaction_date &lt;= #{endDate}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
        </where>
        ORDER BY t.transaction_date DESC
    </select>

    <!-- 更新记录状态 -->
    <update id="updateStatus">
        UPDATE inventory_transaction 
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 检查事务单号是否存在 -->
    <select id="existsByTransactionNum" resultType="int">
        SELECT COUNT(*)
        FROM inventory_transaction
        WHERE transaction_num = #{transactionNum}
    </select>

    <!-- 检查事务单号是否存在（排除指定ID） -->
    <select id="existsByTransactionNumExcludeId" resultType="int">
        SELECT COUNT(*)
        FROM inventory_transaction
        WHERE transaction_num = #{transactionNum} AND id != #{excludeId}
    </select>

    <!-- 根据事务类型统计数量 -->
    <select id="countByTransactionType" resultType="int">
        SELECT COUNT(*)
        FROM inventory_transaction
        WHERE transaction_type = #{transactionType} AND status = 1
    </select>

    <!-- 统计指定时间范围内的出入库数量（按物料规格） -->
    <select id="sumQuantityByDateRangeForSpec" resultType="int">
        SELECT COALESCE(SUM(quantity), 0)
        FROM inventory_transaction
        WHERE material_spec_id = #{materialSpecId}
          AND transaction_type = #{transactionType}
          AND transaction_date >= #{startDate}
          AND transaction_date &lt;= #{endDate}
          AND status = 1
    </select>

    <!-- 统计指定时间范围内的出入库数量（按具体物料） -->
    <select id="sumQuantityByDateRange" resultType="int">
        SELECT COALESCE(SUM(quantity), 0)
        FROM inventory_transaction
        WHERE material_id = #{materialId}
          AND transaction_type = #{transactionType}
          AND transaction_date >= #{startDate}
          AND transaction_date &lt;= #{endDate}
          AND status = 1
    </select>

    <!-- 根据物料规格ID查询出入库记录 -->
    <select id="findByMaterialSpecId" resultMap="TransactionWithAssociationResultMap">
        SELECT 
        <include refid="Transaction_With_Association_Column_List"/>
        FROM inventory_transaction t
        LEFT JOIN material m ON t.material_id = m.id
        LEFT JOIN material_spec ms ON t.material_spec_id = ms.id
        LEFT JOIN supplier s ON t.supplier_id = s.id
        WHERE t.material_spec_id = #{materialSpecId}
        ORDER BY t.transaction_date DESC
    </select>

</mapper>
