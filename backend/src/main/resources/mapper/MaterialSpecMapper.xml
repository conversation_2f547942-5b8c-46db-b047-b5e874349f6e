<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redteamobile.procost.mapper.MaterialSpecMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.redteamobile.procost.entity.MaterialSpec">
        <id column="id" property="id"/>
        <result column="spec_code" property="specCode"/>
        <result column="spec_name" property="specName"/>
        <result column="category" property="category"/>
        <result column="card_type" property="cardType"/>
        <result column="card_level" property="cardLevel"/>
        <result column="environment" property="environment"/>
        <result column="manufacturer" property="manufacturer"/>
        <result column="configuration" property="configuration"/>
        <result column="unit" property="unit"/>
        <result column="specification" property="specification"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, spec_code, spec_name, category, card_type, card_level, environment, 
        manufacturer, configuration, unit, specification, remark, status, 
        create_by, create_time, update_by, update_time
    </sql>

    <!-- 根据规格编码查询物料规格 -->
    <select id="findBySpecCode" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM material_spec
        WHERE spec_code = #{specCode}
    </select>

    <!-- 根据状态查询物料规格 -->
    <select id="findByStatus" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM material_spec
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- 多条件分页查询物料规格 -->
    <select id="findByPage" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM material_spec
        <where>
            <if test="specCode != null and specCode != ''">
                AND spec_code LIKE CONCAT('%', #{specCode}, '%')
            </if>
            <if test="specName != null and specName != ''">
                AND spec_name LIKE CONCAT('%', #{specName}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="cardType != null and cardType != ''">
                AND card_type = #{cardType}
            </if>
            <if test="environment != null and environment != ''">
                AND environment = #{environment}
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                AND manufacturer = #{manufacturer}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据类别查询物料规格 -->
    <select id="findByCategory" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM material_spec
        WHERE category = #{category}
        ORDER BY create_time DESC
    </select>

    <!-- 根据制造商查询物料规格 -->
    <select id="findByManufacturer" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM material_spec
        WHERE manufacturer = #{manufacturer}
        ORDER BY create_time DESC
    </select>

    <!-- 检查规格编码是否存在 -->
    <select id="existsBySpecCode" resultType="int">
        SELECT COUNT(*)
        FROM material_spec
        WHERE spec_code = #{specCode}
    </select>

    <!-- 检查规格编码是否存在（排除指定ID） -->
    <select id="existsBySpecCodeExcludeId" resultType="int">
        SELECT COUNT(*)
        FROM material_spec
        WHERE spec_code = #{specCode} AND id != #{excludeId}
    </select>

    <!-- 根据状态统计物料规格数量 -->
    <select id="countByStatus" resultType="int">
        SELECT COUNT(*)
        FROM material_spec
        WHERE status = #{status}
    </select>

    <!-- 根据类别统计物料规格数量 -->
    <select id="countByCategory" resultType="int">
        SELECT COUNT(*)
        FROM material_spec
        WHERE category = #{category}
    </select>

</mapper>
