<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redteamobile.procost.mapper.MaterialMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.redteamobile.procost.entity.Material">
        <id column="id" property="id"/>
        <result column="eid" property="eid"/>
        <result column="material_spec_id" property="materialSpecId"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 带规格信息的结果映射 -->
    <resultMap id="MaterialWithSpecResultMap" type="com.redteamobile.procost.entity.Material" extends="BaseResultMap">
        <association property="materialSpec" javaType="com.redteamobile.procost.entity.MaterialSpec">
            <id column="spec_id" property="id"/>
            <result column="spec_code" property="specCode"/>
            <result column="spec_name" property="specName"/>
            <result column="category" property="category"/>
            <result column="card_type" property="cardType"/>
            <result column="card_level" property="cardLevel"/>
            <result column="environment" property="environment"/>
            <result column="manufacturer" property="manufacturer"/>
            <result column="configuration" property="configuration"/>
            <result column="unit" property="unit"/>
            <result column="specification" property="specification"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        m.id, m.eid, m.material_spec_id, m.remark, m.status, 
        m.create_by, m.create_time, m.update_by, m.update_time
    </sql>

    <!-- 带规格信息的查询字段 -->
    <sql id="Material_With_Spec_Column_List">
        m.id, m.eid, m.material_spec_id, m.remark, m.status, 
        m.create_by, m.create_time, m.update_by, m.update_time,
        ms.id as spec_id, ms.spec_code, ms.spec_name, ms.category, ms.card_type, 
        ms.card_level, ms.environment, ms.manufacturer, ms.configuration, 
        ms.unit, ms.specification
    </sql>

    <!-- 根据EID号查询物料 -->
    <select id="findByEid" resultMap="MaterialWithSpecResultMap">
        SELECT 
        <include refid="Material_With_Spec_Column_List"/>
        FROM material m
        LEFT JOIN material_spec ms ON m.material_spec_id = ms.id
        WHERE m.eid = #{eid}
    </select>

    <!-- 查询所有物料（带规格信息） -->
    <select id="findAllWithSpec" resultMap="MaterialWithSpecResultMap">
        SELECT 
        <include refid="Material_With_Spec_Column_List"/>
        FROM material m
        LEFT JOIN material_spec ms ON m.material_spec_id = ms.id
        ORDER BY m.create_time DESC
    </select>

    <!-- 根据状态查询物料 -->
    <select id="findByStatus" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM material m
        WHERE m.status = #{status}
        ORDER BY m.create_time DESC
    </select>

    <!-- 多条件分页查询物料 -->
    <select id="findByPage" resultMap="MaterialWithSpecResultMap">
        SELECT 
        <include refid="Material_With_Spec_Column_List"/>
        FROM material m
        LEFT JOIN material_spec ms ON m.material_spec_id = ms.id
        <where>
            <if test="eid != null and eid != ''">
                AND m.eid LIKE CONCAT('%', #{eid}, '%')
            </if>
            <if test="materialSpecId != null">
                AND m.material_spec_id = #{materialSpecId}
            </if>
            <if test="category != null and category != ''">
                AND ms.category = #{category}
            </if>
            <if test="cardType != null and cardType != ''">
                AND ms.card_type = #{cardType}
            </if>
            <if test="environment != null and environment != ''">
                AND ms.environment = #{environment}
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                AND ms.manufacturer = #{manufacturer}
            </if>
            <if test="status != null">
                AND m.status = #{status}
            </if>
        </where>
        ORDER BY m.create_time DESC
    </select>

    <!-- 检查EID号是否存在（排除指定ID） -->
    <select id="existsByEidExcludeId" resultType="int">
        SELECT COUNT(*)
        FROM material
        WHERE eid = #{eid} AND id != #{excludeId}
    </select>

    <!-- 检查EID号是否存在 -->
    <select id="existsByEid" resultType="int">
        SELECT COUNT(*)
        FROM material
        WHERE eid = #{eid}
    </select>

    <!-- 根据状态统计物料数量 -->
    <select id="countByStatus" resultType="int">
        SELECT COUNT(*)
        FROM material
        WHERE status = #{status}
    </select>

    <!-- 根据规格ID查询物料列表 -->
    <select id="findByMaterialSpecId" resultMap="MaterialWithSpecResultMap">
        SELECT 
        <include refid="Material_With_Spec_Column_List"/>
        FROM material m
        LEFT JOIN material_spec ms ON m.material_spec_id = ms.id
        WHERE m.material_spec_id = #{materialSpecId}
        ORDER BY m.create_time DESC
    </select>

</mapper>
