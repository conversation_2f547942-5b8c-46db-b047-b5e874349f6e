<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redteamobile.procost.mapper.InventoryMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.redteamobile.procost.entity.Inventory">
        <id column="id" property="id"/>
        <result column="material_spec_id" property="materialSpecId"/>
        <result column="current_stock" property="currentStock"/>
        <result column="available_stock" property="availableStock"/>
        <result column="reserved_stock" property="reservedStock"/>
        <result column="min_stock" property="minStock"/>
        <result column="max_stock" property="maxStock"/>
        <result column="last_in_time" property="lastInTime"/>
        <result column="last_out_time" property="lastOutTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 带规格信息的结果映射 -->
    <resultMap id="InventoryWithSpecResultMap" type="com.redteamobile.procost.entity.Inventory" extends="BaseResultMap">
        <association property="materialSpec" javaType="com.redteamobile.procost.entity.MaterialSpec">
            <id column="spec_id" property="id"/>
            <result column="spec_code" property="specCode"/>
            <result column="spec_name" property="specName"/>
            <result column="category" property="category"/>
            <result column="card_type" property="cardType"/>
            <result column="card_level" property="cardLevel"/>
            <result column="environment" property="environment"/>
            <result column="manufacturer" property="manufacturer"/>
            <result column="configuration" property="configuration"/>
            <result column="unit" property="unit"/>
            <result column="specification" property="specification"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        i.id, i.material_spec_id, i.current_stock, i.available_stock, i.reserved_stock, 
        i.min_stock, i.max_stock, i.last_in_time, i.last_out_time, i.create_time, i.update_time
    </sql>

    <!-- 带规格信息的查询字段 -->
    <sql id="Inventory_With_Spec_Column_List">
        i.id, i.material_spec_id, i.current_stock, i.available_stock, i.reserved_stock, 
        i.min_stock, i.max_stock, i.last_in_time, i.last_out_time, i.create_time, i.update_time,
        ms.id as spec_id, ms.spec_code, ms.spec_name, ms.category, ms.card_type, 
        ms.card_level, ms.environment, ms.manufacturer, ms.configuration, 
        ms.unit, ms.specification
    </sql>

    <!-- 根据物料规格ID查询库存 -->
    <select id="findByMaterialSpecId" resultMap="InventoryWithSpecResultMap">
        SELECT 
        <include refid="Inventory_With_Spec_Column_List"/>
        FROM inventory i
        LEFT JOIN material_spec ms ON i.material_spec_id = ms.id
        WHERE i.material_spec_id = #{materialSpecId}
    </select>

    <!-- 查询所有库存（带规格信息） -->
    <select id="findAllWithSpec" resultMap="InventoryWithSpecResultMap">
        SELECT 
        <include refid="Inventory_With_Spec_Column_List"/>
        FROM inventory i
        LEFT JOIN material_spec ms ON i.material_spec_id = ms.id
        ORDER BY i.update_time DESC
    </select>

    <!-- 多条件分页查询库存 -->
    <select id="findByPage" resultMap="InventoryWithSpecResultMap">
        SELECT 
        <include refid="Inventory_With_Spec_Column_List"/>
        FROM inventory i
        LEFT JOIN material_spec ms ON i.material_spec_id = ms.id
        <where>
            <if test="materialSpecId != null">
                AND i.material_spec_id = #{materialSpecId}
            </if>
            <if test="category != null and category != ''">
                AND ms.category = #{category}
            </if>
            <if test="cardType != null and cardType != ''">
                AND ms.card_type = #{cardType}
            </if>
            <if test="environment != null and environment != ''">
                AND ms.environment = #{environment}
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                AND ms.manufacturer = #{manufacturer}
            </if>
            <if test="minStock != null">
                AND i.current_stock >= #{minStock}
            </if>
            <if test="maxStock != null">
                AND i.current_stock &lt;= #{maxStock}
            </if>
        </where>
        ORDER BY i.update_time DESC
    </select>

    <!-- 查询库存预警列表 -->
    <select id="findLowStockInventories" resultMap="InventoryWithSpecResultMap">
        SELECT 
        <include refid="Inventory_With_Spec_Column_List"/>
        FROM inventory i
        LEFT JOIN material_spec ms ON i.material_spec_id = ms.id
        WHERE i.current_stock &lt;= i.min_stock AND i.min_stock > 0
        ORDER BY i.current_stock ASC
    </select>

    <!-- 更新库存数量 -->
    <update id="updateStock">
        UPDATE inventory 
        SET current_stock = #{currentStock},
            available_stock = #{availableStock},
            reserved_stock = #{reservedStock},
            update_time = NOW()
        WHERE material_spec_id = #{materialSpecId}
    </update>

    <!-- 更新最后入库时间 -->
    <update id="updateLastInTime">
        UPDATE inventory 
        SET last_in_time = #{lastInTime},
            update_time = NOW()
        WHERE material_spec_id = #{materialSpecId}
    </update>

    <!-- 更新最后出库时间 -->
    <update id="updateLastOutTime">
        UPDATE inventory 
        SET last_out_time = #{lastOutTime},
            update_time = NOW()
        WHERE material_spec_id = #{materialSpecId}
    </update>

    <!-- 根据物料规格ID删除库存 -->
    <delete id="deleteByMaterialSpecId">
        DELETE FROM inventory WHERE material_spec_id = #{materialSpecId}
    </delete>

    <!-- 统计库存总数 -->
    <select id="countAll" resultType="long">
        SELECT COUNT(*) FROM inventory
    </select>

    <!-- 统计预警库存数量 -->
    <select id="countLowStock" resultType="long">
        SELECT COUNT(*) 
        FROM inventory 
        WHERE current_stock &lt;= min_stock AND min_stock > 0
    </select>

</mapper>
