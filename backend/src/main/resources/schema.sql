-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS procost 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE procost;

-- ================================
-- 物料管理系统数据表
-- ================================

-- 1. 供应商表
CREATE TABLE IF NOT EXISTS supplier (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '供应商ID',
    supplier_code VARCHAR(50) NOT NULL UNIQUE COMMENT '供应商编码',
    supplier_name VARCHAR(100) NOT NULL COMMENT '供应商名称',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address VARCHAR(200) COMMENT '地址',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_supplier_name (supplier_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商表';

-- 2. 物料规格表
CREATE TABLE IF NOT EXISTS material_spec (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '规格ID',
    spec_code VARCHAR(50) NOT NULL UNIQUE COMMENT '规格编码',
    spec_name VARCHAR(100) NOT NULL COMMENT '规格名称',
    category VARCHAR(50) COMMENT '物料类别',
    card_type VARCHAR(50) COMMENT '卡片类型',
    card_level VARCHAR(50) COMMENT '卡片等级',
    environment VARCHAR(50) COMMENT '环境（Prod/Staging等）',
    manufacturer VARCHAR(50) COMMENT '制卡商',
    configuration TEXT COMMENT '相关配置信息（JSON格式）',
    unit VARCHAR(20) DEFAULT '张' COMMENT '单位',
    specification VARCHAR(200) COMMENT '规格说明',
    remark TEXT COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '状态：0-停用，1-启用',
    create_by VARCHAR(100) COMMENT '创建人（Keycloak用户ID）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(100) COMMENT '更新人（Keycloak用户ID）',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_spec_code (spec_code),
    INDEX idx_category (category),
    INDEX idx_manufacturer (manufacturer),
    INDEX idx_card_type (card_type),
    INDEX idx_environment (environment)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料规格表';

-- 3. 物料基础信息表
CREATE TABLE IF NOT EXISTS material (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '物料ID',
    eid VARCHAR(50) NOT NULL UNIQUE COMMENT 'EID号',
    material_spec_id BIGINT COMMENT '物料规格ID',
    remark TEXT COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '状态：0-停用，1-启用',
    create_by VARCHAR(100) COMMENT '创建人（Keycloak用户ID）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(100) COMMENT '更新人（Keycloak用户ID）',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_eid (eid),
    INDEX idx_material_spec_id (material_spec_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料基础信息表';

-- 4. 库存主表
CREATE TABLE IF NOT EXISTS inventory (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '库存ID',
    material_spec_id BIGINT NOT NULL COMMENT '物料规格ID',
    current_stock INT DEFAULT 0 COMMENT '当前库存数量',
    available_stock INT DEFAULT 0 COMMENT '可用库存数量',
    reserved_stock INT DEFAULT 0 COMMENT '预留库存数量',
    min_stock INT DEFAULT 0 COMMENT '最小库存预警',
    max_stock INT DEFAULT 0 COMMENT '最大库存上限',
    last_in_time DATETIME COMMENT '最后入库时间',
    last_out_time DATETIME COMMENT '最后出库时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_material_spec_id (material_spec_id),
    INDEX idx_current_stock (current_stock),
    INDEX idx_update_time (update_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存主表';

-- 5. 出入库记录表
CREATE TABLE IF NOT EXISTS inventory_transaction (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '事务ID',
    transaction_num VARCHAR(50) NOT NULL UNIQUE COMMENT '事务单号',
    material_id BIGINT COMMENT '物料ID（具体卡片）',
    material_spec_id BIGINT NOT NULL COMMENT '物料规格ID',
    transaction_type TINYINT NOT NULL COMMENT '事务类型：1-入库，2-出库，3-调拨，4-盘点',
    quantity INT NOT NULL COMMENT '数量',
    unit_price DECIMAL(10,2) COMMENT '单价',
    total_amount DECIMAL(12,2) COMMENT '总金额',
    order_num VARCHAR(100) COMMENT '订单号',
    supplier_id BIGINT COMMENT '供应商ID',
    batch_num VARCHAR(50) COMMENT '批次号',
    operator_id VARCHAR(100) COMMENT '操作人ID（Keycloak用户ID）',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    transaction_date DATETIME NOT NULL COMMENT '事务日期',
    remark TEXT COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '状态：0-已取消，1-已完成',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_transaction_num (transaction_num),
    INDEX idx_material_id (material_id),
    INDEX idx_material_spec_id (material_spec_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_transaction_date (transaction_date),
    INDEX idx_order_num (order_num),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_operator_id (operator_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='出入库记录表';

-- 6. 筛选条件配置表
CREATE TABLE IF NOT EXISTS filter_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    user_id VARCHAR(100) NOT NULL COMMENT '用户ID（Keycloak用户ID）',
    filter_conditions TEXT NOT NULL COMMENT '筛选条件（JSON格式）',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认：0-否，1-是',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_config_name (config_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='筛选条件配置表';

-- 7. 物料标记表
CREATE TABLE IF NOT EXISTS material_mark (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '标记ID',
    material_id BIGINT NOT NULL COMMENT '物料ID',
    user_id VARCHAR(100) NOT NULL COMMENT '用户ID（Keycloak用户ID）',
    mark_color VARCHAR(20) COMMENT '标记颜色',
    mark_label VARCHAR(100) COMMENT '标记标签',
    mark_reason VARCHAR(200) COMMENT '标记原因',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_material_user (material_id, user_id),
    INDEX idx_material_id (material_id),
    INDEX idx_user_id (user_id),
    FOREIGN KEY (material_id) REFERENCES material(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料标记表';

-- 8. 导入导出记录表
CREATE TABLE IF NOT EXISTS import_export_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-导入，2-导出',
    file_name VARCHAR(200) NOT NULL COMMENT '文件名',
    file_path VARCHAR(500) COMMENT '文件路径',
    total_records INT DEFAULT 0 COMMENT '总记录数',
    success_records INT DEFAULT 0 COMMENT '成功记录数',
    failed_records INT DEFAULT 0 COMMENT '失败记录数',
    error_message TEXT COMMENT '错误信息',
    operator_id VARCHAR(100) COMMENT '操作人ID（Keycloak用户ID）',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    status TINYINT DEFAULT 0 COMMENT '状态：0-处理中，1-成功，2-失败',
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    INDEX idx_operation_type (operation_type),
    INDEX idx_operator_id (operator_id),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导入导出记录表';

-- 9. 用户操作记录表
CREATE TABLE IF NOT EXISTS user_operation_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id VARCHAR(100) NOT NULL COMMENT 'Keycloak用户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_module VARCHAR(50) NOT NULL COMMENT '操作模块',
    operation_desc TEXT COMMENT '操作描述',
    request_uri VARCHAR(200) COMMENT '请求URI',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_params TEXT COMMENT '请求参数',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_time (operation_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作记录表';

-- ================================
-- 初始化数据
-- ================================

-- 插入示例供应商数据
INSERT INTO supplier (supplier_code, supplier_name, contact_email, address) VALUES 
('SUP001', '恒宝股份', '<EMAIL>', '北京市海淀区'),
('SUP002', '东信和平', '<EMAIL>', '上海市浦东新区'),
('SUP003', '华大电子', '<EMAIL>', '深圳市南山区')
ON DUPLICATE KEY UPDATE supplier_name=supplier_name;

-- 插入示例物料规格数据
INSERT INTO material_spec (spec_code, spec_name, category, card_type, card_level, environment, manufacturer, configuration, specification) VALUES 
('SPEC001', '5*6贴片卡规格', '预埋种子卡', '5*6贴片卡', '消费级', 'Prod', '恒宝', '{"type":"5*6","package":"贴片"}', '标准AID'),
('SPEC002', '三切插拔卡规格', '预埋种子卡', '三切插拔卡', '消费级', 'Prod', '东信和平', '{"type":"三切","package":"插拔"}', '标准AID'),
('SPEC003', '2.5*2.7贴片封装规格', '预埋种子卡', '2.5*2.7贴片封装', '消费级', 'Staging', '华大电子', '{"type":"2.5*2.7","package":"贴片"}', '标准AID')
ON DUPLICATE KEY UPDATE spec_name=spec_name;

-- 插入示例物料数据
INSERT INTO material (eid, material_spec_id) VALUES
('EID001', 1),
('EID002', 2),
('EID003', 3)
ON DUPLICATE KEY UPDATE eid=eid;

-- 为每个物料规格创建库存记录
INSERT INTO inventory (material_spec_id, current_stock, available_stock, min_stock, max_stock) VALUES
(1, 0, 0, 100, 10000),
(2, 0, 0, 100, 10000),
(3, 0, 0, 100, 10000)
ON DUPLICATE KEY UPDATE current_stock=current_stock;
